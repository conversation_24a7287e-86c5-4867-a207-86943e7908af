import { View, TouchableOpacity, Image } from 'react-native';
import React, { useState, useContext } from 'react';
import Ionicons from '@expo/vector-icons/Ionicons';
import SimpleLineIcons from '@expo/vector-icons/SimpleLineIcons';
import OptionModal from '../../Components/Home/OptionModal';
import { ThemeContext } from '../../../context/ThemeContext';

export default function Header() {
    const { theme } = useContext(ThemeContext);
    const [OptionModalVisible, setOptionModalVisible] = useState(false);
    return (
        <View
            style={{
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                paddingHorizontal: 12,
                paddingTop: 8,
                paddingBottom: 8,
            }}
        >
            <OptionModal
                OptionModalVisible={OptionModalVisible}
                setOptionModalVisible={setOptionModalVisible}
            />
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <Image
                    source={require('../../../assets/images/logo.png')}
                    style={{
                        height: 40,
                        width: 40,
                        borderRadius: 50,
                        borderWidth: 0.5,
                        borderColor: theme.WHITE,
                    }}
                />
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                <TouchableOpacity className="rounded-full">
                    <Ionicons
                        name="search"
                        size={24}
                        color={theme.WHITE}
                        style={{ padding: 8 }}
                    />
                </TouchableOpacity>
                <TouchableOpacity>
                    <Ionicons
                        name="notifications"
                        size={24}
                        color={theme.WHITE}
                        style={{ padding: 8 }}
                    />
                </TouchableOpacity>
                <TouchableOpacity onPress={() => setOptionModalVisible(true)}>
                    <SimpleLineIcons
                        name="options-vertical"
                        size={20}
                        color={theme.WHITE}
                        style={{ padding: 8 }}
                    />
                </TouchableOpacity>
            </View>
        </View>
    );
}
