import React, { useContext } from 'react';
import { Text } from 'react-native';
import CategorySection from '../Shared/CategorySection';
import { ThemeContext } from '../../../context/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { fetchAllContractors } from '../../../api/contractor/contractorApi';
import { useRouter } from 'expo-router';

export default function Contractors() {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();

    const { data: contractors, isLoading, isError, error } = useQuery({
        queryKey: ['allContractors'],
        queryFn: fetchAllContractors,
    });

    const handleContractorPress = (contractor) => {
        router.push({
            pathname: '/Contractors/ContractorProfile',
            params: { contractorId: contractor._id }
        });
    };

    if (isLoading) {
        return <Text style={{ color: theme.TEXT_PRIMARY, textAlign: 'center', padding: 20 }}>Loading contractors...</Text>;
    }

    if (isError) {
        return <Text style={{ color: theme.ERROR, textAlign: 'center', padding: 20 }}>Error: {error.message}</Text>;
    }

    // Transform contractor data to match Card component expectations - only avatar, name, serviceArea, and ratings
    const transformedContractors = contractors?.map(contractor => ({
        id: contractor._id,
        _id: contractor._id,
        name: contractor.user?.name || 'Unknown Contractor',
        title: contractor.user?.name || 'Unknown Contractor',
        image: contractor.avatar || require('../../../assets/images/icon.png'),
        rating: contractor.ratings || 0,
        serviceAreas: contractor.serviceAreas || []
    })) || [];

    return (
        <CategorySection
            title="Top Contractors"
            data={transformedContractors}
            onItemPress={handleContractorPress}
            viewAllRoute="/Contractors/ContractorList"
        />
    );
}
