import React, { useContext } from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { fetchBrokerProfileById } from '../../api/broker/brokerApi';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import Ionicons from '@expo/vector-icons/Ionicons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';

const { width, height } = Dimensions.get('window');

export default function BrokerProfile() {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();
  const { brokerId } = useLocalSearchParams();

  const { data: broker, isLoading, isError, error } = useQuery({
    queryKey: ['brokerProfile', brokerId],
    queryFn: () => fetchBrokerProfileById(brokerId),
    enabled: !!brokerId,
  });

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={{ color: theme.TEXT_PRIMARY, textAlign: 'center', marginTop: 50 }}>
          Loading broker profile...
        </Text>
      </SafeAreaView>
    );
  }

  if (isError) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <BackButton color={theme.PRIMARY} />
        <Text style={{ color: theme.ERROR, textAlign: 'center', marginTop: 50 }}>
          Error: {error.message}
        </Text>
      </SafeAreaView>
    );
  }

  const handleHire = () => {
    // Static button as requested - no functionality
    console.log('Hire button pressed for broker:', broker?.user?.name);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      />
      
      <BackButton color={theme.WHITE} />
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={[styles.avatarContainer, { borderColor: theme.WHITE }]}>
            <Image
              source={
                broker?.avatar
                  ? { uri: broker.avatar }
                  : require('../../assets/images/icon.png')
              }
              style={styles.avatar}
              resizeMode="cover"
            />
          </View>
          
          <Text style={[styles.name, { color: theme.WHITE }]}>
            {broker?.user?.name || 'Unknown Broker'}
          </Text>
          
          {broker?.verificationStatus === 'verified' && (
            <View style={styles.verifiedBadge}>
              <Ionicons name="checkmark-circle" size={20} color={theme.WHITE} />
              <Text style={[styles.verifiedText, { color: theme.WHITE }]}>Verified</Text>
            </View>
          )}
        </View>

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={[styles.statCard, { backgroundColor: theme.CARD, shadowColor: theme.SHADOW }]}>
            <MaterialIcons name="work" size={24} color={theme.PRIMARY} />
            <Text style={[styles.statNumber, { color: theme.PRIMARY }]}>
              {broker?.experience || 0}
            </Text>
            <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Years Exp.</Text>
          </View>
          
          <View style={[styles.statCard, { backgroundColor: theme.CARD, shadowColor: theme.SHADOW }]}>
            <Ionicons name="star" size={24} color={theme.PRIMARY} />
            <Text style={[styles.statNumber, { color: theme.PRIMARY }]}>
              {broker?.ratings?.toFixed(1) || '0.0'}
            </Text>
            <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Rating</Text>
          </View>
          
          <View style={[styles.statCard, { backgroundColor: theme.CARD, shadowColor: theme.SHADOW }]}>
            <Ionicons name="location" size={24} color={theme.PRIMARY} />
            <Text style={[styles.statNumber, { color: theme.PRIMARY }]}>
              {broker?.serviceAreas?.length || 0}
            </Text>
            <Text style={[styles.statLabel, { color: theme.TEXT_SECONDARY }]}>Areas</Text>
          </View>
        </View>

        {/* Service Areas */}
        {broker?.serviceAreas && broker.serviceAreas.length > 0 && (
          <View style={[styles.section, { backgroundColor: theme.CARD, shadowColor: theme.SHADOW }]}>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>Service Areas</Text>
            <View style={styles.tagsContainer}>
              {broker.serviceAreas.map((area, index) => (
                <View key={index} style={[styles.tag, { backgroundColor: theme.PRIMARY + '20' }]}>
                  <Text style={[styles.tagText, { color: theme.PRIMARY }]}>{area}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Hire Button */}
        <TouchableOpacity
          style={[styles.hireButton, { backgroundColor: theme.PRIMARY, shadowColor: theme.SHADOW }]}
          onPress={handleHire}
          activeOpacity={0.8}
        >
          <LinearGradient
            colors={[theme.PRIMARY, theme.SECONDARY]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.hireButtonGradient}
          >
            <MaterialIcons name="handshake" size={24} color={theme.WHITE} />
            <Text style={[styles.hireButtonText, { color: theme.WHITE }]}>HIRE</Text>
          </LinearGradient>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: height * 0.35,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  profileHeader: {
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 30,
  },
  avatarContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    padding: 4,
    marginBottom: 16,
  },
  avatar: {
    width: '100%',
    height: '100%',
    borderRadius: 56,
  },
  name: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  verifiedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  verifiedText: {
    marginLeft: 6,
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  statCard: {
    alignItems: 'center',
    padding: 20,
    borderRadius: 16,
    flex: 1,
    marginHorizontal: 5,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 4,
  },
  section: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    fontSize: 14,
    fontWeight: '600',
  },
  hireButton: {
    margin: 20,
    borderRadius: 16,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  hireButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  hireButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginLeft: 8,
  },
});
