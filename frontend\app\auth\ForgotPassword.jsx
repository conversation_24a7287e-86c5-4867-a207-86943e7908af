import { View, Text, Image, TouchableOpacity, Pressable, TextInput, StyleSheet, Dimensions, KeyboardAvoidingView, Platform, ScrollView, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useState, useContext } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { privateAPIClient } from '../../api';
import BackButton from '../Components/Shared/BackButton';
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';

const { width, height } = Dimensions.get('window');

export default function ForgotPassword() {
    const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
    const [email, setEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');

    const router = useRouter();

    // Email validation regex
    const validateEmail = (email) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleSubmit = async () => {
        // Reset error state
        setError('');
        
        // Validate email
        if (!email.trim()) {
            setError('Please enter your email address');
            return;
        }
        
        if (!validateEmail(email)) {
            setError('Please enter a valid email address');
            return;
        }
        
        setIsLoading(true);
        
        try {
            // Call the correct API endpoint for password reset
            await privateAPIClient.get('/user-service/api/v1/forgot-password', { email });
            // Show success message
            showToast('success', 'Success', 'An OTP has been sent to your email.', 'OK', '/auth/VerifyOTP');
        } catch (e) {
            showToast('success', 'Success', 'An error occurred. Please try again.', 'OK', '/auth/VerifyOTP');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container,{backgroundColor: theme.BACKGROUND,}]}
        >
            <ScrollView 
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
            >
                <BackButton color={theme.WHITE}/>

                {/* Background Image */}
                <View style={styles.backgroundContainer}>
                    <Image 
                        source={require('../../assets/images/background.png')}
                        style={styles.backgroundImage}
                        resizeMode="cover"
                    />
                    <LinearGradient
                        colors={['rgba(42, 142, 158, 0.7)', theme.PRIMARY]}
                        style={styles.backgroundOverlay}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 0, y: 1 }}
                    />
                </View>

                {/* Content Container */}
                <View style={styles.contentContainer}>
                    {/* Logo */}
                    <View style={styles.logoContainer}>
                        <Image
                            source={require('../../assets/images/build-connect.jpg')}
                            style={[styles.logo,{borderColor:theme.LOGO_BORDER,}]}
                            resizeMode="contain"
                        />
                    </View>

                    {/* Form Container */}
                    <View style={[styles.formContainer,{backgroundColor: theme.CARD,shadowColor: theme.SHADOW,}]}>
                        <Text style={[styles.title,{color: theme.PRIMARY,}]}>Forgot Password</Text>
                        <Text style={[styles.subtitle,{color:theme.TEXT_SECONDARY,}]}>Please enter your email to reset the password</Text>

                        {/* Error message */}
                        {error ? <Text style={styles.errorText}>{error}</Text> : null}

                        {/* Email Input */}
                        <View style={[
                            styles.inputContainer,{backgroundColor: theme.INPUT_BACKGROUND,borderColor: theme.INPUT_BORDER,},
                            error ? styles.inputError : null
                        ]}>
                            <Ionicons name="mail-outline" size={22} color={error ? 'red' : theme.PRIMARY} style={styles.inputIcon} />
                            <TextInput
                                placeholder="Email"
                                value={email}
                                onChangeText={(text) => {
                                    setEmail(text);
                                    if (error) setError('');
                                }}
                                placeholderTextColor={theme.TEXT_PLACEHOLDER}
                                style={[styles.input,{color:theme.TEXT_PRIMARY,}]}
                                autoCapitalize="none"
                                keyboardType="email-address"
                                autoComplete="email"
                                textContentType="emailAddress"
                            />
                            {email.length > 0 && (
                                <TouchableOpacity 
                                    onPress={() => setEmail('')}
                                    style={styles.clearButton}
                                >
                                    <Ionicons name="close-circle" size={20} color="#999" />
                                </TouchableOpacity>
                            )}
                        </View>

                        {/* Send OTP Button */}
                        <TouchableOpacity
                            onPress={handleSubmit}
                            style={[styles.loginButton,{shadowColor: theme.PRIMARY,}]}
                            disabled={isLoading}
                        >
                            <LinearGradient
                                colors={[theme.PRIMARY, theme.SECONDARY]}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 1, y: 0 }}
                                style={styles.loginButtonGradient}
                            >
                                {isLoading ? (
                                    <View style={styles.loadingContainer}>
                                        <ActivityIndicator color="white" size="small" />
                                        <Text style={styles.loginButtonText}>Sending...</Text>
                                    </View>
                                ) : (
                                    <Text style={styles.loginButtonText}>Send OTP</Text>
                                )}
                            </LinearGradient>
                        </TouchableOpacity>

                        {/* Login Link */}
                        <View style={styles.signupContainer}>
                            <Text style={[styles.signupText,{color:theme.TEXT_SECONDARY,}]}>Already have an account?</Text>
                            <Pressable 
                                onPress={() => router.push('/auth/Login')}
                                style={({ pressed }) => [
                                    { opacity: pressed ? 0.7 : 1 }
                                ]}
                            >
                                <Text style={[styles.signupLink,{color: theme.PRIMARY,}]}>Login Here</Text>
                            </Pressable>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom:0,
        height: height * 0.6,
        zIndex:-1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
    },
    logoContainer: {
        alignItems: 'center',
        marginTop: height * 0.02,
        marginBottom: height * 0.04,
    },
    logo: {
        width: 120,
        height: 120,
        borderWidth: 3,
        borderRadius: 60,
    },
    formContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 16,
        marginBottom: 24,
        textAlign:'center'
    },
    errorText: {
        color: 'red',
        marginBottom: 10,
        fontSize: 14,
    },
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 16,
        paddingHorizontal: 12,
        height: 56,
    },
    inputError: {
        borderColor: 'red',
        borderWidth: 1,
    },
    inputIcon: {
        marginRight: 12,
    },
    input: {
        flex: 1,
        height: '100%',
        fontSize: 16,
    },
    clearButton: {
        padding: 8,
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    loginButton: {
        borderRadius: 12,
        overflow: 'hidden',
        marginBottom: 14,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    loginButtonGradient: {
        paddingVertical: 16,
        alignItems: 'center',
    },
    loginButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
        marginLeft: 8,
    },
    signupContainer: {
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
    },
    signupText: {
        fontSize: 14,
        marginRight: 4,
    },
    signupLink: {
        fontSize: 14,
        fontWeight: 'bold',
    },
});
