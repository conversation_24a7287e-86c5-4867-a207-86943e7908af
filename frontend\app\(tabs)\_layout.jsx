import React, { useState,useContext } from 'react';
import { View, TouchableOpacity, Animated, Easing } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { LinearGradient } from 'react-native-svg';
import Ionicons from 'react-native-vector-icons/Ionicons';
import Home from './Home';
import Chats from './Chats';
import Profile from './Profile';
import ListingsScreen from './Listings';
import FABModal from '../Components/Home/FabModal';
import '../../global.css';
import { AntDesign, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';

const Tab = createBottomTabNavigator();

export default function App() {
  const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
  const [modalVisible, setModalVisible] = useState(false);
  const [fabScale] = useState(new Animated.Value(1));
  const [fabGlow] = useState(new Animated.Value(0));

  const handleFabPressIn = () => {
    Animated.parallel([
      Animated.timing(fabScale, {
        toValue: 0.92,
        duration: 150,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
      Animated.timing(fabGlow, {
        toValue: 1,
        duration: 150,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handleFabPressOut = () => {
    Animated.parallel([
      Animated.timing(fabScale, {
        toValue: 1,
        duration: 150,
        easing: Easing.ease,
        useNativeDriver: true,
      }),
      Animated.timing(fabGlow, {
        toValue: 0,
        duration: 150,
        useNativeDriver: false,
      }),
    ]).start();
  };

  return (
    <>
      <FABModal modalVisible={modalVisible} setModalVisible={setModalVisible} />
      <Tab.Navigator
        initialRouteName="Home"
        screenOptions={({ route }) => ({
          tabBarActiveTintColor: theme.PRIMARY,
          tabBarInactiveTintColor: theme.GRAY,
          tabBarStyle: {
            position: 'absolute',
            height: 80, // Adjust as needed
            paddingTop: 12,
            paddingBottom: 12,
            backgroundColor: theme.BACKGROUND,
            borderTopWidth: 0,
            elevation: 12,
            shadowColor: theme.SHADOW,
            shadowOffset: { width: 0, height: -3 },
            shadowOpacity: 0.15,
            shadowRadius: 8,
            borderRadius: 16,
            marginHorizontal: 8,
            marginBottom: 8,
          },
          headerShown: false,
          tabBarLabelStyle: {
            fontSize: 10,
            fontWeight: '700',
          },
        })}
      >
        <Tab.Screen
          name="Home"
          component={Home}
          options={{
            tabBarIcon: ({ color, focused }) => (
              <Ionicons
                name={focused ? 'home' : 'home-outline'}
                size={28}
                color={color}
                style={focused ? { transform: [{ scale: 1.15 }] } : {}}
              />
            ),
            tabBarLabel: 'Home',
          }}
        />

        <Tab.Screen
          name="Listings"
          component={ListingsScreen}
          options={{
            tabBarIcon: ({ color, focused }) => (
              <Ionicons
                name={focused ? 'bookmark' : 'bookmark-outline'}
                size={28}
                color={color}
                style={focused ? { transform: [{ scale: 1.15 }] } : {}}
              />
            ),
            tabBarLabel: 'Listings',
          }}
        />

        <Tab.Screen
                    name='Add'
                    component={() => null}
                    options={{
                        tabBarButton: (props) => (
                            <View style={{ top:-2, left:9,right:9, backgroundColor:theme.BORDER, borderWidth: 2, borderColor:theme.PRIMARY, width:'48', height:'48',
                            ...props.style }} 
                            className="rounded-full justify-center items-center shadow-fab absolute align-center" >
                                <TouchableOpacity className=""
                                    activeOpacity={0.8}
                                    onPress={() => setModalVisible(true)}>
                                    <AntDesign name='pluscircle' size={42} color={theme.PRIMARY} />
                                </TouchableOpacity>
                            </View>
                        ),
                    }}
                />

        <Tab.Screen
          name="Chats"
          component={Chats}
          options={{
            tabBarIcon: ({ color, focused }) => (
              <Ionicons
                name={focused ? 'chatbubble-ellipses' : 'chatbubble-ellipses-outline'}
                size={28}
                color={color}
                style={focused ? { transform: [{ scale: 1.15 }] } : {}}
              />
            ),
            tabBarLabel: 'Chats',
          }}
        />

        <Tab.Screen
          name="Profile"
          component={Profile}
          options={{
            tabBarIcon: ({ color, focused }) => (
              <Ionicons
                name={focused ? 'person' : 'person-outline'}
                size={28}
                color={color}
                style={focused ? { transform: [{ scale: 1.15 }] } : {}}
              />
            ),
            tabBarLabel: 'Profile',
          }}
        />
      </Tab.Navigator>
    </>
  );
}
