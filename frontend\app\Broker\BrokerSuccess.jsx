import React, { useRef, useEffect, useContext } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Easing,
  Image,
  Dimensions,
  ScrollView,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useRouter } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { ThemeContext } from "../../context/ThemeContext";
import BackButton from "../Components/Shared/BackButton";

const { width, height } = Dimensions.get("window");

const BrokerSuccess = () => {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();

  // Animation values
  const scaleAnim = useRef(new Animated.Value(0.7)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 7,
        tension: 60,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        easing: Easing.out(Easing.exp),
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <View style={{ flex: 1, backgroundColor: theme.BACKGROUND }}>
      {/* Background Image & Gradient */}
      <View style={styles.backgroundContainer}>
        <Image
          source={require("../../assets/images/background.png")}
          style={styles.backgroundImage}
          resizeMode="cover"
        />
        <LinearGradient
          colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
          style={styles.backgroundOverlay}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
        />
      </View>
      <ScrollView contentContainerStyle={{ flexGrow: 1, minHeight: height }}>
        <BackButton color={theme.WHITE} onPress={()=> router.push('./Home')}/>
        <View style={styles.contentContainer}>
          <View style={[styles.card, { backgroundColor: theme.CARD, shadowColor: theme.SHADOW }]}>
            <Animated.View
              style={[
                styles.iconWrapper,
                { shadowColor: theme.PRIMARY },
                {
                  transform: [{ scale: scaleAnim }],
                  opacity: fadeAnim,
                },
              ]}
            >
              <Ionicons name="checkmark-circle" size={90} color={theme.PRIMARY} />
            </Animated.View>
            <Animated.Text style={[styles.title, { color: theme.PRIMARY, opacity: fadeAnim }]}>
              Application Submitted!
            </Animated.Text>
            <Animated.Text style={[styles.subtitle, { color: theme.TEXT_PRIMARY, opacity: fadeAnim }]}>
              Your Site Scout application is pending verification.
            </Animated.Text>
            <Animated.Text style={[styles.nextSteps, { color: theme.TEXT_SECONDARY, opacity: fadeAnim }]}>
              Our team will review your details and contact you soon. You will be notified once your account is verified.
            </Animated.Text>
            <TouchableOpacity
              onPress={() => router.push("../Profile/Applications")}
              activeOpacity={0.85}
              style={styles.button}
            >
              <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.buttonGradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
                <View style={{flexDirection:'row',alignItems:'center', justifyContent:'space-between',paddingHorizontal: 4, gap:8}}>
                <Text style={[styles.buttonText, { color: theme.WHITE }]}>View Application</Text>
                <Ionicons name="chevron-forward" size={18} color={theme.WHITE} style={styles.buttonIcon} />
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  backgroundContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    height: height,
    opacity: 0.8,
    zIndex: 0,
  },
  backgroundImage: {
    width: "100%",
    height: "100%",
  },
  backgroundOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.9,
  },
  contentContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    minHeight: height * 0.8,
  },
  card: {
    width: "90%",
    maxWidth: 400,
    borderRadius: 20,
    padding: 32,
    alignItems: "center",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 6,
    marginTop: height * -0.03,
  },
  iconWrapper: {
    marginBottom: 24,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.18,
    shadowRadius: 16,
    elevation: 8,
    backgroundColor: "rgba(255,255,255,0.10)",
    borderRadius: 60,
    padding: 12,
  },
  title: {
    fontSize: 26,
    fontWeight: "bold",
    marginBottom: 10,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 10,
    fontWeight: "500",
  },
  nextSteps: {
    fontSize: 14,
    textAlign: "center",
    marginBottom: 28,
    color: "#888",
    lineHeight: 20,
  },
  button: {
    width: "100%",
    borderRadius: 12,
    overflow: "hidden",
    marginTop: 8,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.18,
    shadowRadius: 8,
    elevation: 4,
  },
  buttonGradient: {
    paddingVertical: 16,
    alignItems: "center",
    borderRadius: 12,
  },
  buttonText: {
    fontSize: 18,
    fontWeight: "bold",
  },
});

export default BrokerSuccess;
