// components/BackButton.js
import React, {useContext} from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { ThemeContext } from '../../../context/ThemeContext';

export default function BackButton({ color = '#000', size = 24, style }) {
  const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
  const navigation = useNavigation();
  return (
    <TouchableOpacity
      onPress={() => navigation.goBack()}
      style={[styles.button, style]}
      accessibilityLabel="Go back"
    >
      <Ionicons name="arrow-back" size={size} color={color} />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    padding: 12,
    justifyContent: 'left',
    alignItems: 'left',
    color:'transparent',
  },
});
