const mongoose = require('mongoose');

const contractorSchema = new mongoose.Schema(
    {
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true,
            unique: true,
        },
        serviceAreas: {
            type: [String],
            default: [],
        },
        specialties: {
            type: [String],
            default: [],
        },
        portfolio: {
            type: [String],
            default: [],
        },
        experience: {
            type: Number,
            default: 0,
        },
        ratings: {
            type: Number,
            default: 0,
        },
        verificationStatus: {
            type: String,
            enum: ['pending', 'verified', 'rejected'],
            default: 'pending',
        },
        verifiedBy: {
            type: mongoose.Schema.Types.ObjectId,
        },
        approvalDate: {
            type: Date,
        },
        reasonForRejection: {
            type: String,
            default: '',
        },
    },
    {
        timestamps: true,
    }
);

const Contractor = mongoose.model('Contractor', contractorSchema);
module.exports = Contractor;
