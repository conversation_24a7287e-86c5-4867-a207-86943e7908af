import React, { useEffect, useContext } from "react";
import {
  ScrollView,
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  StatusBar,
  Alert,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { ThemeContext } from "../../context/ThemeContext";
import { showToast } from "../../utils/showToast";
import ProfileUpdateModal from "../Components/Profile/ProfileUpdateModal";
import { useQuery, useMutation } from "@tanstack/react-query";
import { fetchUserProfile, updateUserProfile } from "../../api/user/userApi";
import queryClient from "../../api/queryClient";
import BackButton from "../Components/Shared/BackButton";

const { height } = Dimensions.get("window");

const PROFILE_OPTIONS = [
  {
    key: "professional",
    icon: "briefcase-outline",
    label: "Professional Details",
    visibleto: ["contractor", "broker"],
    url: "/Profile/Professional",
  },
  {
    key: "applications",
    icon: "receipt-outline",
    label: "Manage Applications",
    visibleto: ["user", "contractor", "broker"],
    url: "/Profile/Applications",
  },
  {
    key: "payments",
    icon: "card-outline",
    label: "Manage Payments",
    visibleto: ["user", "contractor", "broker"],
    url: "/Profile/Payments",
  },
  {
    key: "hiring",
    icon: "hourglass-outline",
    label: "Hiring History",
    visibleto: ["user"],
    url: "/Profile/Hiring",
  },
  {
    key: "tickets",
    icon: "chatbubbles-outline",
    label: "Raise Tickets",
    visibleto: ["user", "contractor", "broker"],
    url: "/Profile/Tickets",
  },
  {
    key: "settings",
    icon: "settings-outline",
    label: "Settings",
    visibleto: ["user", "contractor", "broker"],
    url: "/Profile/Settings",
  },
  {
    key: "logout",
    icon: "log-out-outline",
    label: "Logout",
    visibleto: ["user", "contractor", "broker"],
    url: "/auth/Login",
  },
];

export default function ProfileSection() {
  const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
  const router = useRouter();
  const [userData, setUserData] = React.useState({
    name: "",
    email: "",
    phone: "",
    avatarUri: null,
    role: "",
  });
  const [isExtendCard, setExtendCard] = React.useState(false);

  const { data: user, isLoading: isUserLoading, error: userError } = useQuery({
    queryKey: ["userProfile"],
    queryFn: fetchUserProfile,
    refetchInterval: 30000,
    refetchIntervalInBackground: false,
    onError: () => {
      showToast("error", "Error", "Failed to fetch user profile.");
    },
  });

  const { mutate: updateProfile, isLoading: isUpdating } = useMutation({
    mutationFn: updateUserProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userProfile"] });
      showToast("success", "Success", "Profile updated successfully.");
    },
    onError: () => {
      showToast("error", "Error", "Failed to update profile.");
    },
  });

  useEffect(() => {
    if (user) {
      setUserData(user);
    }
  }, [user]);

  const handleSaveProfile = async (updatedData) => {
    if (!updatedData.name.trim()) {
      showToast("error", "Error", "Name is required");
      return;
    }
    if (updatedData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(updatedData.email)) {
      showToast("error", "Error", "Invalid email format");
      return;
    }
    updateProfile(updatedData);
    setUserData(updatedData);
  };

  const handleLogout = () => {
    Alert.alert(
      "Confirm Logout",
      "Are you sure you want to log out?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Logout",
          style: "destructive",
          onPress: async () => {
            try {
              await AsyncStorage.removeItem("authToken");
              router.replace("/auth/Login");
            } catch (error) {
              showToast("error", "Error", "Failed to logout.");
            }
          },
        },
      ]
    );
  };

  const handleOptionPress = (url, key) => {
    if (key === "logout") {
      handleLogout();
    } else {
      router.push(url);
    }
  };

  const filteredOptions = PROFILE_OPTIONS.filter((option) =>
    option.visibleto.includes(userData.role?.toLowerCase() || "user")
  );

  if (isUserLoading) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center", backgroundColor: theme.BACKGROUND }}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
      </View>
    );
  }

  if (userError) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center", backgroundColor: theme.BACKGROUND }}>
        <Text style={{ color: theme.TEXT_PRIMARY }}>Failed to load profile. Please try again.</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: theme.BACKGROUND }}>
      <StatusBar barStyle={isDarkMode ? "light-content" : "dark-content"} backgroundColor={theme.PRIMARY} />
      <ScrollView contentContainerStyle={{ flexGrow: 1, minHeight: height }}>
        <BackButton color={theme.WHITE} testID="back-button" />

        <View style={{ position: "absolute", top: 0, left: 0, right: 0, minHeight: height * 0.5, zIndex: -1 }}>
          <Image
            source={require("../../assets/images/background.png")}
            style={{ width: "100%", height: "100%" }}
            resizeMode="cover"
          />
          <LinearGradient
            colors={[theme.GRADIENT_PRIMARY, theme.GRADIENT_SECONDARY]}
            style={{ position: "absolute", top: 0, left: 0, right: 0, bottom: 0 }}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          />
        </View>

        <View style={{ flex: 1, alignItems: "center" }}>
          <View style={{ width: "90%", maxWidth: 400, backgroundColor: theme.CARD, borderRadius: 20, padding: 24, elevation: 5 }}>
            <View style={{ alignItems: "center", position: "relative" }}>
              <TouchableOpacity
                style={{
                  position: "absolute",
                  top: -16,
                  right: -16,
                  padding: 8,
                  backgroundColor: theme.PRIMARY + "25",
                  borderRadius: 20,
                }}
                onPress={toggleTheme}
                accessibilityLabel="Toggle Dark Mode"
                testID="theme-toggle"
              >
                <Ionicons name={isDarkMode ? "moon" : "moon-outline"} size={24} color={theme.PRIMARY} />
              </TouchableOpacity>
              <View style={{ flexDirection: "row", justifyContent: "center", alignItems: "center", width: "100%", marginTop: 30, paddingHorizontal: 40 }}>
                <View style={{ backgroundColor: theme.PRIMARY, borderRadius: 40, width: 80, height: 80, justifyContent: "center", alignItems: "center", overflow: "hidden", marginBottom: 16 }}>
                  {userData.avatarUri ? (
                    <Image
                      source={{ uri: userData.avatarUri }}
                      style={{ width: "100%", height: "100%" }}
                      accessibilityLabel={`Profile picture for ${userData.name}`}
                    />
                  ) : (
                    <Text style={{ color: theme.WHITE, fontSize: 30, fontWeight: "bold" }}>
                      {userData.name.charAt(0).toUpperCase()}
                    </Text>
                  )}
                </View>

                <View style={{ flexDirection: "column", justifyContent: "center", alignItems: "center", width: "100%" }}>
                  <View style={{ flexDirection: "row", justifyContent: "center", gap: 3, alignItems: "center", marginBottom: 8 }}>
                    <Text style={{ fontSize: 24, fontWeight: "bold", textAlign: "center", color: theme.TEXT_PRIMARY, marginRight: 8 }}>
                      {userData.name || "User"}
                    </Text>
                    <TouchableOpacity onPress={() => setExtendCard(true)} accessibilityLabel="Extend Profile Settings" testID="edit-profile">
                      <Ionicons name="chevron-down-outline" size={20} color={theme.PRIMARY} />
                    </TouchableOpacity>
                  </View>
                  <Text style={{ fontSize: 16, color: theme.TEXT_SECONDARY, marginBottom: 4 }}>
                    {userData.email || "No email provided"}
                  </Text>
                  <Text style={{ fontSize: 16, color: theme.TEXT_SECONDARY }}>
                    {userData.phone || "No phone provided"}
                  </Text>
                </View>
              </View>
            </View>

            <View style={{ borderTopWidth: StyleSheet.hairlineWidth, borderTopColor: theme.INPUT_BORDER, marginTop: 16 }}>
              {filteredOptions.map(({ key, icon, label, url }) => (
                <TouchableOpacity
                  key={key}
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                    paddingVertical: 15,
                    borderBottomWidth: StyleSheet.hairlineWidth,
                    borderBottomColor: theme.INPUT_BORDER,
                  }}
                  onPress={() => handleOptionPress(url, key)}
                  accessibilityLabel={label}
                  accessibilityRole="button"
                  testID={`${key}-option`}
                >
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <Ionicons name={icon} size={22} color={theme.PRIMARY} />
                    <Text style={{ marginLeft: 15, fontSize: 16, color: theme.TEXT_PRIMARY }}>{label}</Text>
                  </View>
                  <Ionicons name="chevron-forward-outline" size={20} color={theme.TEXT_SECONDARY} />
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        <ProfileUpdateModal
          isVisible={isExtendCard}
          onClose={() => setExtendCard(false)}
          theme={theme}
          userData={userData}
          onSave={handleSaveProfile}
          isLoading={isUpdating}
        />
      </ScrollView>
    </View>
  );
}