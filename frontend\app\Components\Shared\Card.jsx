import { View, Text, Image, TouchableOpacity, Animated } from 'react-native';
import React, { useContext } from 'react';
import Ionicons from '@expo/vector-icons/Ionicons';
import { ThemeContext } from '../../../context/ThemeContext';

// Card component for general use - simplified for broker/contractor cards (avatar, name, serviceArea, ratings)
export default function Card({
  title,
  name,
  location,
  price,
  area,
  image,
  verified,
  rating,
  projects,
  serviceAreas,
  onPress,
  onInterestedPress,
  accessibilityLabel,
}) {
    const { theme } = useContext(ThemeContext);
    const scaleAnim = React.useRef(new Animated.Value(1)).current;

    const handlePressIn = () => {
        Animated.spring(scaleAnim, {
            toValue: 0.98,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    const handlePressOut = () => {
        Animated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    // Fallback for image: if image is a string (uri), use {uri: image}
    let imageSource = image;
    if (typeof image === 'string') {
        imageSource = { uri: image };
    }

    return (
        <TouchableOpacity
            style={{
                backgroundColor: theme.CARD,
                borderRadius: 16,
                overflow: 'hidden',
                shadowColor: theme.SHADOW,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.12,
                shadowRadius: 8,
                elevation: 4,
                marginRight: 16,
                width: 250,
                minHeight: 260,
            }}
            onPress={onPress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            activeOpacity={0.9}
            accessibilityLabel={accessibilityLabel}
        >
            <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
                {/* Image Section */}
                <View style={{ position: 'relative' }}>
                    <Image
                        source={imageSource}
                        style={{
                            width: '100%',
                            height: 140,
                            borderTopLeftRadius: 16,
                            borderTopRightRadius: 16,
                            backgroundColor: theme.INPUT_BACKGROUND,
                        }}
                        resizeMode="cover"
                    />
                    <View
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            backgroundColor: 'rgba(0,0,0,0.45)',
                            padding: 8,
                            borderBottomLeftRadius: 16,
                            borderBottomRightRadius: 16,
                        }}
                    >
                        <Text
                            style={{
                                color: theme.WHITE,
                                fontSize: 16,
                                fontWeight: 'bold',
                            }}
                        >
                            {title || name}
                        </Text>
                    </View>
                </View>

        {/* Content Section */}
        <View style={{ padding: 12 }}>
          {location && (
            <View style={{ flexDirection: "row", alignItems: "center", marginTop: 2 }}>
              <Ionicons name="location-outline" size={14} color={theme.GRAY} />
              <Text style={{ color: theme.TEXT_SECONDARY, fontSize: 13, marginLeft: 4 }}>{location}</Text>
            </View>
          )}
          {price && (
            <Text style={{ color: theme.PRIMARY, fontWeight: "bold", marginTop: 2 }}>{price}</Text>
          )}
          {area && (
            <Text style={{ color: theme.TEXT_SECONDARY, fontSize: 13, marginTop: 2 }}>{area}</Text>
          )}
          {verified && (
            <View style={{ flexDirection: "row", alignItems: "center", marginTop: 2 }}>
              <Ionicons name="checkmark-circle" size={16} color={theme.PRIMARY} />
              <Text style={{ color: theme.PRIMARY, fontSize: 13, marginLeft: 4 }}>Verified by Site Scout</Text>
            </View>
          )}
          {rating && rating > 0 && (
            <View style={{ flexDirection: "row", alignItems: "center", marginTop: 2 }}>
              <Ionicons name="star" size={14} color={theme.PRIMARY} />
              <Text style={{ color: theme.PRIMARY, fontWeight: "bold", marginLeft: 4 }}>{rating.toFixed(1)}</Text>
            </View>
          )}
          {serviceAreas && serviceAreas.length > 0 && (
            <View style={{ flexDirection: "row", alignItems: "center", marginTop: 2 }}>
              <Ionicons name="location-outline" size={14} color={theme.GRAY} />
              <Text style={{ color: theme.TEXT_SECONDARY, fontSize: 13, marginLeft: 4 }}>
                {serviceAreas.slice(0, 2).join(', ')}{serviceAreas.length > 2 ? '...' : ''}
              </Text>
            </View>
          )}
          {projects && (
            <Text style={{ color: theme.TEXT_SECONDARY, fontSize: 13, marginTop: 2 }}>{projects} Projects</Text>
          )}
          {onInterestedPress && (
            <TouchableOpacity
              style={{
                backgroundColor: theme.PRIMARY,
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 8,
                marginTop: 10,
                alignItems: "center",
              }}
              onPress={onInterestedPress}
              accessibilityLabel={`Show interest in ${title || name}`}
            >
              <Text style={{ color: theme.WHITE, fontWeight: "600", fontSize: 15 }}>Interested</Text>
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
}

// BrokerCard: for broker profile display
export function BrokerCard({
  name,
  nameOnAadhaar,
  aadhaarNumber,
  panNumber,
  panName,
  dateOfBirth,
  gender,
  address,
  experience,
  serviceAreas,
  onPress,
  accessibilityLabel,
}) {
    const { theme } = useContext(ThemeContext);
    const scaleAnim = React.useRef(new Animated.Value(1)).current;

    const handlePressIn = () => {
        Animated.spring(scaleAnim, {
            toValue: 0.98,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    const handlePressOut = () => {
        Animated.spring(scaleAnim, {
            toValue: 1,
            friction: 8,
            tension: 100,
            useNativeDriver: true,
        }).start();
    };

    const formatDate = (date) => {
        if (!date) return '';
        const d = new Date(date);
        return d.toLocaleDateString();
    };

    return (
        <TouchableOpacity
            style={{
                backgroundColor: theme.CARD,
                borderRadius: 16,
                overflow: 'hidden',
                shadowColor: theme.SHADOW,
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.12,
                shadowRadius: 8,
                elevation: 4,
                marginRight: 16,
                width: 270,
                minHeight: 260,
            }}
            onPress={onPress}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            activeOpacity={0.9}
            accessibilityLabel={accessibilityLabel}
        >
            <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
                <View
                    style={{
                        padding: 14,
                        borderBottomWidth: 1,
                        borderBottomColor: theme.INPUT_BORDER,
                        backgroundColor: theme.PRIMARY + '10%',
                    }}
                >
                    <Text
                        style={{
                            color: theme.PRIMARY,
                            fontWeight: 'bold',
                            fontSize: 18,
                            marginBottom: 2,
                        }}
                    >
                        {name || panName || nameOnAadhaar || 'Broker'}
                    </Text>
                    <Text style={{ color: theme.TEXT_SECONDARY, fontSize: 13 }}>
                        Aadhaar: {aadhaarNumber}
                    </Text>
                    <Text style={{ color: theme.TEXT_SECONDARY, fontSize: 13 }}>
                        PAN: {panNumber}
                    </Text>
                </View>
                <View style={{ padding: 14 }}>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                        }}
                    >
                        <Ionicons
                            name="person-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                            }}
                        >
                            Name on Aadhaar: {nameOnAadhaar}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                        }}
                    >
                        <Ionicons
                            name="calendar-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                            }}
                        >
                            DOB: {formatDate(dateOfBirth)}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                        }}
                    >
                        <Ionicons
                            name="male-female-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                            }}
                        >
                            Gender: {gender}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                        }}
                    >
                        <Ionicons
                            name="home-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                            }}
                        >
                            Address: {address}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                        }}
                    >
                        <Ionicons
                            name="briefcase-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                            }}
                        >
                            Experience: {experience} year
                            {experience > 1 ? 's' : ''}
                        </Text>
                    </View>
                    <View
                        style={{
                            flexDirection: 'row',
                            alignItems: 'center',
                            marginBottom: 4,
                            flexWrap: 'wrap',
                        }}
                    >
                        <Ionicons
                            name="location-outline"
                            size={16}
                            color={theme.PRIMARY}
                        />
                        <Text
                            style={{
                                color: theme.TEXT_PRIMARY,
                                fontSize: 14,
                                marginLeft: 6,
                                flexShrink: 1,
                            }}
                        >
                            Service Areas:{' '}
                            {Array.isArray(serviceAreas)
                                ? serviceAreas.join(', ')
                                : ''}
                        </Text>
                    </View>
                </View>
            </Animated.View>
        </TouchableOpacity>
    );
}
