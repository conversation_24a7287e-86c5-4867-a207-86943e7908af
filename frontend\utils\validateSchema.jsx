import * as Yup from "yup";

export const validationSchema = Yup.object().shape({
    aadhaarNumber: Yup.string()
      .matches(/^\d{12}$/, "Enter a valid 12-digit Aadhaar number")
      .required("Aadhaar number is required"),
    nameOnAadhaar: Yup.string()
      .matches(
        /^[a-zA-Z\s.]+$/,
        "Name on Aadhaar can only contain letters, spaces, and dots"
      )
      .min(2, "Name must be at least 2 characters")
      .max(50, "Name must not exceed 50 characters")
      .required("Name on Aadhaar is required"),
    dateOfBirth: Yup.date()
      .transform((value, originalValue) => {
        if (!originalValue) return null;
        const parsed = new Date(originalValue);
        return isNaN(parsed) ? null : parsed;
      })
      .required("Date of birth is required")
      .test("age-range", "Age must be between 18 and 100 years", (value) => {
        if (!value) return false;
        const birthDate = new Date(value);
        const today = new Date();
        const age = today.getFullYear() - birthDate.getFullYear();
        return age >= 18 && age <= 100;
      }),
    gender: Yup.string()
      .oneOf(["Male", "Female", "Other"], "Select a gender")
      .required("Gender is required"),
    address: Yup.string()
      .min(10, "Address must be at least 10 characters")
      .max(500, "Address must not exceed 500 characters")
      .required("Address is required"),
    panNumber: Yup.string()
      .matches(
        /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
        "Enter a valid 10-character PAN number"
      )
      .required("PAN number is required"),
    panName: Yup.string()
      .matches(
        /^[a-zA-Z\s.]+$/,
        "Name on PAN can only contain letters, spaces, and dots"
      )
      .min(2, "Name must be at least 2 characters")
      .max(50, "Name must not exceed 50 characters")
      .required("Name on PAN is required"),
    panDateOfBirth: Yup.date()
      .transform((value, originalValue) => {
        if (!originalValue) return null;
        const parsed = new Date(originalValue);
        return isNaN(parsed) ? null : parsed;
      })
      .required("Date of birth is required")
      .test(
        "age-range-pan",
        "Age must be between 18 and 100 years",
        (value) => {
          if (!value) return false;
          const birthDate = new Date(value);
          const today = new Date();
          const age = today.getFullYear() - birthDate.getFullYear();
          return age >= 18 && age <= 100;
        }
      ),
    experience: Yup.number()
      .typeError("Enter a valid non-negative number")
      .min(0, "Enter a valid non-negative number")
      .required("Experience is required"),
    serviceAreas: Yup.array()
      .of(Yup.string().trim().min(1, "Area cannot be empty"))
      .min(1, "Enter at least one service area")
      .max(10, "Enter at most 10 service areas")
      .required("Enter at least one service area"),
    aadhaarDocument: Yup.mixed().required("Aadhaar document is required"),
    panDocument: Yup.mixed().required("PAN document is required"),
  });