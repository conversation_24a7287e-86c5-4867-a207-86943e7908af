import React, { useContext } from 'react';
import { Text } from 'react-native';
import CategorySection from '../Shared/CategorySection';
import { ThemeContext } from '../../../context/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { fetchAllBrokers } from '../../../api/broker/brokerApi';
import { useRouter } from 'expo-router';

export default function Brokers() {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();

    const { data: brokers, isLoading, isError, error } = useQuery({
        queryKey: ['allBrokers'],
        queryFn: fetchAllBrokers,
    });

    const handleBrokerPress = (broker) => {
        router.push({
            pathname: '/Brokers/BrokerProfile',
            params: { brokerId: broker._id }
        });
    };

    if (isLoading) {
        return <Text style={{ color: theme.TEXT_PRIMARY, textAlign: 'center', padding: 20 }}>Loading brokers...</Text>;
    }

    if (isError) {
        return <Text style={{ color: theme.ERROR, textAlign: 'center', padding: 20 }}>Error: {error.message}</Text>;
    }

    // Transform broker data to match Card component expectations
    const transformedBrokers = brokers?.map(broker => ({
        id: broker._id,
        _id: broker._id,
        name: broker.user?.name || 'Unknown Broker',
        title: broker.user?.name || 'Unknown Broker',
        image: broker.avatar || require('../../../assets/images/icon.png'),
        rating: broker.ratings || 0,
        serviceAreas: broker.serviceAreas || [],
        experience: broker.experience || 0,
        verified: broker.verificationStatus === 'verified'
    })) || [];

    return (
        <CategorySection
            title="Featured Brokers"
            role="broker"
            data={transformedBrokers}
            onItemPress={handleBrokerPress}
            viewAllRoute="/Brokers/BrokerList"
        />
    );
}
