import React, { useContext, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
  FlatList,
  Modal,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from '@expo/vector-icons/Ionicons';
import { ThemeContext } from '../../../context/ThemeContext';
import { fetchContractorProfileById } from '../../../frontend/api/contractor/contractorApi';
import BackButton from '../Components/Shared/BackButton';

const { width } = Dimensions.get('window');

export default function ContractorProfile() {
  const { theme } = useContext(ThemeContext);
  const { contractorId } = useLocalSearchParams();
  const router = useRouter();
  const [selectedPortfolioItem, setSelectedPortfolioItem] = useState(null);
  const [portfolioModalVisible, setPortfolioModalVisible] = useState(false);

  const { data: contractor, isLoading, isError, error } = useQuery({
    queryKey: ['contractorProfile', contractorId],
    queryFn: () => fetchContractorProfileById(contractorId),
    enabled: !!contractorId,
  });

  const openPortfolioModal = (item) => {
    setSelectedPortfolioItem(item);
    setPortfolioModalVisible(true);
  };

  const closePortfolioModal = () => {
    setPortfolioModalVisible(false);
    setSelectedPortfolioItem(null);
  };

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.BACKGROUND }}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={{ color: theme.TEXT_PRIMARY, marginTop: 10 }}>Loading contractor profile...</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.BACKGROUND }}>
        <Text style={{ color: theme.ERROR, textAlign: 'center', padding: 20 }}>
          Error loading contractor profile: {error.message}
        </Text>
      </View>
    );
  }

  const renderPortfolioItem = ({ item, index }) => (
    <TouchableOpacity
      style={{
        width: (width - 60) / 3,
        aspectRatio: 1,
        marginRight: index % 3 === 2 ? 0 : 10,
        marginBottom: 10,
        borderRadius: 8,
        overflow: 'hidden',
      }}
      onPress={() => openPortfolioModal(item)}
    >
      <Image
        source={{ uri: item.image }}
        style={{ width: '100%', height: '100%' }}
        resizeMode="cover"
      />
    </TouchableOpacity>
  );

  return (
    <ScrollView style={{ flex: 1, backgroundColor: theme.BACKGROUND }}>
      {/* Header with Back Button */}
      <View style={{ 
        flexDirection: 'row', 
        alignItems: 'center', 
        padding: 20, 
        paddingTop: 50,
        backgroundColor: theme.CARD 
      }}>
        <BackButton />
        <Text style={{ 
          color: theme.TEXT_PRIMARY, 
          fontSize: 18, 
          fontWeight: 'bold', 
          marginLeft: 15 
        }}>
          Contractor Profile
        </Text>
      </View>

      {/* Profile Header */}
      <LinearGradient
        colors={[theme.PRIMARY + '20', theme.BACKGROUND]}
        style={{ padding: 20, alignItems: 'center' }}
      >
        <Image
          source={contractor?.image ? { uri: contractor.image } : require('../../../assets/images/icon.png')}
          style={{
            width: 120,
            height: 120,
            borderRadius: 60,
            borderWidth: 4,
            borderColor: theme.PRIMARY,
            backgroundColor: theme.INPUT_BACKGROUND,
          }}
          resizeMode="cover"
        />
        <Text style={{
          color: theme.TEXT_PRIMARY,
          fontSize: 24,
          fontWeight: 'bold',
          marginTop: 15,
          textAlign: 'center',
        }}>
          {contractor?.name || 'Unknown Contractor'}
        </Text>
        
        {/* Rating */}
        {contractor?.ratings > 0 && (
          <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 8 }}>
            <Ionicons name="star" size={20} color={theme.PRIMARY} />
            <Text style={{ 
              color: theme.PRIMARY, 
              fontSize: 18, 
              fontWeight: 'bold', 
              marginLeft: 5 
            }}>
              {contractor.ratings.toFixed(1)}
            </Text>
          </View>
        )}
      </LinearGradient>

      {/* Details Section */}
      <View style={{ padding: 20 }}>
        {/* Service Areas */}
        {contractor?.serviceAreas && contractor.serviceAreas.length > 0 && (
          <View style={{
            backgroundColor: theme.CARD,
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
              <Ionicons name="location" size={20} color={theme.PRIMARY} />
              <Text style={{
                color: theme.TEXT_PRIMARY,
                fontSize: 16,
                fontWeight: 'bold',
                marginLeft: 8,
              }}>
                Service Areas
              </Text>
            </View>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
              {contractor.serviceAreas.map((area, index) => (
                <View
                  key={index}
                  style={{
                    backgroundColor: theme.PRIMARY + '20',
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 20,
                    marginRight: 8,
                    marginBottom: 8,
                  }}
                >
                  <Text style={{ color: theme.PRIMARY, fontSize: 14 }}>{area}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Specialties */}
        {contractor?.specialties && contractor.specialties.length > 0 && (
          <View style={{
            backgroundColor: theme.CARD,
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
              <Ionicons name="construct" size={20} color={theme.PRIMARY} />
              <Text style={{
                color: theme.TEXT_PRIMARY,
                fontSize: 16,
                fontWeight: 'bold',
                marginLeft: 8,
              }}>
                Specialties
              </Text>
            </View>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
              {contractor.specialties.map((specialty, index) => (
                <View
                  key={index}
                  style={{
                    backgroundColor: theme.SECONDARY + '20',
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 20,
                    marginRight: 8,
                    marginBottom: 8,
                  }}
                >
                  <Text style={{ color: theme.SECONDARY, fontSize: 14 }}>{specialty}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Experience */}
        {contractor?.experience > 0 && (
          <View style={{
            backgroundColor: theme.CARD,
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Ionicons name="briefcase" size={20} color={theme.PRIMARY} />
              <Text style={{
                color: theme.TEXT_PRIMARY,
                fontSize: 16,
                fontWeight: 'bold',
                marginLeft: 8,
              }}>
                Experience: {contractor.experience} year{contractor.experience > 1 ? 's' : ''}
              </Text>
            </View>
          </View>
        )}

        {/* Portfolio Section */}
        {contractor?.portfolio && contractor.portfolio.length > 0 && (
          <View style={{
            backgroundColor: theme.CARD,
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
              <Ionicons name="images" size={20} color={theme.PRIMARY} />
              <Text style={{
                color: theme.TEXT_PRIMARY,
                fontSize: 16,
                fontWeight: 'bold',
                marginLeft: 8,
              }}>
                Portfolio ({contractor.portfolio.length} items)
              </Text>
            </View>
            <FlatList
              data={contractor.portfolio}
              renderItem={renderPortfolioItem}
              numColumns={3}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          </View>
        )}

        {/* Availability Status */}
        <View style={{
          backgroundColor: theme.CARD,
          borderRadius: 12,
          padding: 16,
          marginBottom: 20,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Ionicons 
              name={contractor?.isAvailable ? "checkmark-circle" : "close-circle"} 
              size={20} 
              color={contractor?.isAvailable ? theme.SUCCESS : theme.ERROR} 
            />
            <Text style={{
              color: theme.TEXT_PRIMARY,
              fontSize: 16,
              fontWeight: 'bold',
              marginLeft: 8,
            }}>
              {contractor?.isAvailable ? 'Available for hire' : 'Currently unavailable'}
            </Text>
          </View>
        </View>

        {/* Hire Button */}
        <TouchableOpacity
          style={{
            backgroundColor: theme.PRIMARY,
            borderRadius: 12,
            padding: 16,
            alignItems: 'center',
            marginTop: 10,
          }}
          onPress={() => {
            // Navigate to hire/contact functionality
            console.log('Hire contractor:', contractor?.name);
          }}
        >
          <Text style={{
            color: theme.WHITE,
            fontSize: 18,
            fontWeight: 'bold',
          }}>
            HIRE CONTRACTOR
          </Text>
        </TouchableOpacity>
      </View>

      {/* Portfolio Modal */}
      <Modal
        visible={portfolioModalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={closePortfolioModal}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.9)',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
          <TouchableOpacity
            style={{ position: 'absolute', top: 50, right: 20, zIndex: 1 }}
            onPress={closePortfolioModal}
          >
            <Ionicons name="close" size={30} color="white" />
          </TouchableOpacity>
          
          {selectedPortfolioItem && (
            <View style={{ width: '90%', alignItems: 'center' }}>
              <Image
                source={{ uri: selectedPortfolioItem.image }}
                style={{ width: '100%', aspectRatio: 1, borderRadius: 8 }}
                resizeMode="contain"
              />
              <Text style={{
                color: 'white',
                fontSize: 16,
                marginTop: 20,
                textAlign: 'center',
                paddingHorizontal: 20,
              }}>
                {selectedPortfolioItem.caption}
              </Text>
            </View>
          )}
        </View>
      </Modal>
    </ScrollView>
  );
}
