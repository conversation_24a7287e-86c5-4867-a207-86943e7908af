import React, { useState, useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Image,
  TextInput,
  Alert,
  Modal,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import * as ImagePicker from 'expo-image-picker';
import { 
  fetchContractorProfileById, 
  addPortfolioItem, 
  updatePortfolioItem, 
  deletePortfolioItem 
} from '../../api/contractor/contractorApi';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import Ionicons from '@expo/vector-icons/Ionicons';
import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { showToast } from '../../utils/showToast';

const { width, height } = Dimensions.get('window');

export default function PortfolioManager() {
  const { theme } = useContext(ThemeContext);
  const router = useRouter();
  const queryClient = useQueryClient();
  
  const [modalVisible, setModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [caption, setCaption] = useState('');

  // This would normally get the contractor ID from user context or params
  const contractorId = 'current-contractor-id'; // Replace with actual contractor ID

  const { data: contractor, isLoading } = useQuery({
    queryKey: ['contractorProfile', contractorId],
    queryFn: () => fetchContractorProfileById(contractorId),
  });

  const addMutation = useMutation({
    mutationFn: ({ contractorId, formData }) => addPortfolioItem(contractorId, formData),
    onSuccess: () => {
      queryClient.invalidateQueries(['contractorProfile', contractorId]);
      setModalVisible(false);
      resetForm();
      showToast('success', 'Success', 'Portfolio item added successfully!');
    },
    onError: (error) => {
      showToast('error', 'Error', error.message || 'Failed to add portfolio item');
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ contractorId, portfolioItemId, formData }) => 
      updatePortfolioItem(contractorId, portfolioItemId, formData),
    onSuccess: () => {
      queryClient.invalidateQueries(['contractorProfile', contractorId]);
      setModalVisible(false);
      resetForm();
      showToast('success', 'Success', 'Portfolio item updated successfully!');
    },
    onError: (error) => {
      showToast('error', 'Error', error.message || 'Failed to update portfolio item');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: ({ contractorId, portfolioItemId }) => 
      deletePortfolioItem(contractorId, portfolioItemId),
    onSuccess: () => {
      queryClient.invalidateQueries(['contractorProfile', contractorId]);
      showToast('success', 'Success', 'Portfolio item deleted successfully!');
    },
    onError: (error) => {
      showToast('error', 'Error', error.message || 'Failed to delete portfolio item');
    },
  });

  const resetForm = () => {
    setSelectedImage(null);
    setCaption('');
    setEditingItem(null);
  };

  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      showToast('error', 'Permission Required', 'Camera roll permissions are required!');
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });

    if (!result.canceled) {
      setSelectedImage(result.assets[0]);
    }
  };

  const handleSave = () => {
    if (!selectedImage && !editingItem) {
      showToast('error', 'Error', 'Please select an image');
      return;
    }
    if (!caption.trim()) {
      showToast('error', 'Error', 'Please enter a caption');
      return;
    }

    const formData = new FormData();
    if (selectedImage) {
      formData.append('image', {
        uri: selectedImage.uri,
        type: 'image/jpeg',
        name: 'portfolio-image.jpg',
      });
    }
    formData.append('caption', caption.trim());

    if (editingItem) {
      updateMutation.mutate({
        contractorId,
        portfolioItemId: editingItem.id,
        formData,
      });
    } else {
      addMutation.mutate({ contractorId, formData });
    }
  };

  const handleEdit = (item, index) => {
    setEditingItem({ ...item, id: index });
    setCaption(item.caption);
    setModalVisible(true);
  };

  const handleDelete = (index) => {
    Alert.alert(
      'Delete Portfolio Item',
      'Are you sure you want to delete this portfolio item?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteMutation.mutate({ contractorId, portfolioItemId: index }),
        },
      ]
    );
  };

  const renderPortfolioItem = (item, index) => (
    <View key={index} style={[styles.portfolioItem, { backgroundColor: theme.CARD, shadowColor: theme.SHADOW }]}>
      <Image source={{ uri: item.image }} style={styles.portfolioImage} resizeMode="cover" />
      <View style={styles.portfolioContent}>
        <Text style={[styles.portfolioCaption, { color: theme.TEXT_PRIMARY }]} numberOfLines={2}>
          {item.caption}
        </Text>
        <View style={styles.portfolioActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.PRIMARY + '20' }]}
            onPress={() => handleEdit(item, index)}
          >
            <MaterialIcons name="edit" size={16} color={theme.PRIMARY} />
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: theme.ERROR + '20' }]}
            onPress={() => handleDelete(index)}
          >
            <MaterialIcons name="delete" size={16} color={theme.ERROR} />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
        <Text style={{ color: theme.TEXT_PRIMARY, textAlign: 'center', marginTop: 50 }}>
          Loading portfolio...
        </Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.BACKGROUND }]}>
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.headerGradient}
      />
      
      <BackButton color={theme.WHITE} />
      
      <View style={styles.header}>
        <Text style={[styles.headerTitle, { color: theme.WHITE }]}>Portfolio Manager</Text>
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: 'rgba(255,255,255,0.2)' }]}
          onPress={() => setModalVisible(true)}
        >
          <Ionicons name="add" size={24} color={theme.WHITE} />
        </TouchableOpacity>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.portfolioGrid}>
          {contractor?.portfolio?.map((item, index) => renderPortfolioItem(item, index))}
          {(!contractor?.portfolio || contractor.portfolio.length === 0) && (
            <View style={styles.emptyState}>
              <MaterialIcons name="photo-library" size={64} color={theme.GRAY} />
              <Text style={[styles.emptyText, { color: theme.TEXT_SECONDARY }]}>
                No portfolio items yet
              </Text>
              <Text style={[styles.emptySubtext, { color: theme.GRAY }]}>
                Add your first portfolio item to showcase your work
              </Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Add/Edit Modal */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => {
          setModalVisible(false);
          resetForm();
        }}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: theme.BACKGROUND }]}>
          <View style={[styles.modalHeader, { borderBottomColor: theme.BORDER }]}>
            <TouchableOpacity onPress={() => { setModalVisible(false); resetForm(); }}>
              <Text style={[styles.modalCancel, { color: theme.PRIMARY }]}>Cancel</Text>
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>
              {editingItem ? 'Edit Portfolio Item' : 'Add Portfolio Item'}
            </Text>
            <TouchableOpacity onPress={handleSave}>
              <Text style={[styles.modalSave, { color: theme.PRIMARY }]}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <TouchableOpacity
              style={[styles.imagePickerButton, { backgroundColor: theme.CARD, borderColor: theme.BORDER }]}
              onPress={pickImage}
            >
              {selectedImage || editingItem ? (
                <Image
                  source={{ uri: selectedImage?.uri || editingItem?.image }}
                  style={styles.selectedImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.imagePickerPlaceholder}>
                  <MaterialIcons name="add-a-photo" size={48} color={theme.GRAY} />
                  <Text style={[styles.imagePickerText, { color: theme.TEXT_SECONDARY }]}>
                    Tap to select image
                  </Text>
                </View>
              )}
            </TouchableOpacity>

            <Text style={[styles.inputLabel, { color: theme.TEXT_PRIMARY }]}>Caption</Text>
            <TextInput
              style={[styles.captionInput, { 
                backgroundColor: theme.CARD, 
                borderColor: theme.BORDER,
                color: theme.TEXT_PRIMARY 
              }]}
              placeholder="Describe your work..."
              placeholderTextColor={theme.GRAY}
              value={caption}
              onChangeText={setCaption}
              multiline
              maxLength={500}
              textAlignVertical="top"
            />
            <Text style={[styles.characterCount, { color: theme.GRAY }]}>
              {caption.length}/500
            </Text>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 120,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    padding: 20,
  },
  portfolioGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  portfolioItem: {
    width: (width - 50) / 2,
    marginBottom: 20,
    borderRadius: 12,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  portfolioImage: {
    width: '100%',
    height: 120,
  },
  portfolioContent: {
    padding: 12,
  },
  portfolioCaption: {
    fontSize: 14,
    lineHeight: 18,
    marginBottom: 8,
  },
  portfolioActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    width: '100%',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalCancel: {
    fontSize: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalSave: {
    fontSize: 16,
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  imagePickerButton: {
    height: 200,
    borderRadius: 12,
    borderWidth: 2,
    borderStyle: 'dashed',
    marginBottom: 20,
    overflow: 'hidden',
  },
  selectedImage: {
    width: '100%',
    height: '100%',
  },
  imagePickerPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePickerText: {
    marginTop: 12,
    fontSize: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  captionInput: {
    height: 100,
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    marginBottom: 8,
  },
  characterCount: {
    fontSize: 12,
    textAlign: 'right',
  },
});
