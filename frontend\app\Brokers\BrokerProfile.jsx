import React, { useContext } from 'react';
import {
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import { LinearGradient } from 'expo-linear-gradient';
import Ionicons from '@expo/vector-icons/Ionicons';
import { ThemeContext } from '../../../context/ThemeContext';
import { fetchBrokerProfileById } from '../../../api/broker/brokerApi';
import BackButton from '../Components/Shared/BackButton';

const { width } = Dimensions.get('window');

export default function BrokerProfile() {
  const { theme } = useContext(ThemeContext);
  const { brokerId } = useLocalSearchParams();
  const router = useRouter();

  const { data: broker, isLoading, isError, error } = useQuery({
    queryKey: ['brokerProfile', brokerId],
    queryFn: () => fetchBrokerProfileById(brokerId),
    enabled: !!brokerId,
  });

  if (isLoading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.BACKGROUND }}>
        <ActivityIndicator size="large" color={theme.PRIMARY} />
        <Text style={{ color: theme.TEXT_PRIMARY, marginTop: 10 }}>Loading broker profile...</Text>
      </View>
    );
  }

  if (isError) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.BACKGROUND }}>
        <Text style={{ color: theme.ERROR, textAlign: 'center', padding: 20 }}>
          Error loading broker profile: {error.message}
        </Text>
      </View>
    );
  }

  return (
    <ScrollView style={{ flex: 1, backgroundColor: theme.BACKGROUND }}>
      {/* Header with Back Button */}
      <View style={{ 
        flexDirection: 'row', 
        alignItems: 'center', 
        padding: 20, 
        paddingTop: 50,
        backgroundColor: theme.CARD 
      }}>
        <BackButton />
        <Text style={{ 
          color: theme.TEXT_PRIMARY, 
          fontSize: 18, 
          fontWeight: 'bold', 
          marginLeft: 15 
        }}>
          Broker Profile
        </Text>
      </View>

      {/* Profile Header */}
      <LinearGradient
        colors={[theme.PRIMARY + '20', theme.BACKGROUND]}
        style={{ padding: 20, alignItems: 'center' }}
      >
        <Image
          source={broker?.image ? { uri: broker.image } : require('../../../assets/images/icon.png')}
          style={{
            width: 120,
            height: 120,
            borderRadius: 60,
            borderWidth: 4,
            borderColor: theme.PRIMARY,
            backgroundColor: theme.INPUT_BACKGROUND,
          }}
          resizeMode="cover"
        />
        <Text style={{
          color: theme.TEXT_PRIMARY,
          fontSize: 24,
          fontWeight: 'bold',
          marginTop: 15,
          textAlign: 'center',
        }}>
          {broker?.name || 'Unknown Broker'}
        </Text>
        
        {/* Rating */}
        {broker?.ratings > 0 && (
          <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 8 }}>
            <Ionicons name="star" size={20} color={theme.PRIMARY} />
            <Text style={{ 
              color: theme.PRIMARY, 
              fontSize: 18, 
              fontWeight: 'bold', 
              marginLeft: 5 
            }}>
              {broker.ratings.toFixed(1)}
            </Text>
          </View>
        )}
      </LinearGradient>

      {/* Details Section */}
      <View style={{ padding: 20 }}>
        {/* Service Areas */}
        {broker?.serviceAreas && broker.serviceAreas.length > 0 && (
          <View style={{
            backgroundColor: theme.CARD,
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 12 }}>
              <Ionicons name="location" size={20} color={theme.PRIMARY} />
              <Text style={{
                color: theme.TEXT_PRIMARY,
                fontSize: 16,
                fontWeight: 'bold',
                marginLeft: 8,
              }}>
                Service Areas
              </Text>
            </View>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
              {broker.serviceAreas.map((area, index) => (
                <View
                  key={index}
                  style={{
                    backgroundColor: theme.PRIMARY + '20',
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 20,
                    marginRight: 8,
                    marginBottom: 8,
                  }}
                >
                  <Text style={{ color: theme.PRIMARY, fontSize: 14 }}>{area}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Experience */}
        {broker?.experience > 0 && (
          <View style={{
            backgroundColor: theme.CARD,
            borderRadius: 12,
            padding: 16,
            marginBottom: 16,
          }}>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Ionicons name="briefcase" size={20} color={theme.PRIMARY} />
              <Text style={{
                color: theme.TEXT_PRIMARY,
                fontSize: 16,
                fontWeight: 'bold',
                marginLeft: 8,
              }}>
                Experience: {broker.experience} year{broker.experience > 1 ? 's' : ''}
              </Text>
            </View>
          </View>
        )}

        {/* Availability Status */}
        <View style={{
          backgroundColor: theme.CARD,
          borderRadius: 12,
          padding: 16,
          marginBottom: 20,
        }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Ionicons 
              name={broker?.isAvailable ? "checkmark-circle" : "close-circle"} 
              size={20} 
              color={broker?.isAvailable ? theme.SUCCESS : theme.ERROR} 
            />
            <Text style={{
              color: theme.TEXT_PRIMARY,
              fontSize: 16,
              fontWeight: 'bold',
              marginLeft: 8,
            }}>
              {broker?.isAvailable ? 'Available for hire' : 'Currently unavailable'}
            </Text>
          </View>
        </View>

        {/* Hire Button */}
        <TouchableOpacity
          style={{
            backgroundColor: theme.PRIMARY,
            borderRadius: 12,
            padding: 16,
            alignItems: 'center',
            marginTop: 10,
          }}
          onPress={() => {
            // Navigate to hire/contact functionality
            console.log('Hire broker:', broker?.name);
          }}
        >
          <Text style={{
            color: theme.WHITE,
            fontSize: 18,
            fontWeight: 'bold',
          }}>
            HIRE BROKER
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}
