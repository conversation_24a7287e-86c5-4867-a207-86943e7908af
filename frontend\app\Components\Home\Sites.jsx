import React, { useEffect, useState,useContext } from 'react';
import CategorySection from '../Shared/CategorySection';
import { useRouter } from 'expo-router';
import { ThemeContext } from '../../../context/ThemeContext';
import { showToast } from '../../../utils/showToast';

const fetchSites = async () => {
  // Simulated API response
  return [
    {
      id: '1',
      title: 'Urban Oasis Plot',
      location: 'City Center',
      price: '$275,000',
      image: require('../../../assets/images/image1.png'),
      area: '6,000 sqft',
      broker: { name: '<PERSON>', id: 'broker1' },
      verified: true,
    },
    {
      id: '2',
      title: 'Riverfront Land',
      location: 'Riverside',
      price: '$320,000',
      image: require('../../../assets/images/image2.png'),
      area: '8,000 sqft',
      broker: { name: '<PERSON>', id: 'broker2' },
      verified: true,
    },
    {
      id: '3',
      title: 'Tech Park Plot',
      location: 'Tech District',
      price: '$400,000',
      image: require('../../../assets/images/image3.png'),
      area: '10,000 sqft',
      broker: { name: 'Sophia Patel', id: 'broker3' },
      verified: true,
    },
    {
      id: '4',
      title: 'Green Valley Land',
      location: 'Suburbs',
      price: '$220,000',
      image: require('../../../assets/images/icon.png'),
      area: '5,500 sqft',
      broker: { name: 'Noah Kim', id: 'broker4' },
      verified: true,
    },
  ];
};

export default function Sites() {
  const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
  const router = useRouter();
  const [sites, setSites] = useState([]);

  useEffect(() => {
    const loadSites = async () => {
      try {
        const data = await fetchSites();
        setSites(data);
      } catch (error) {
        showToast('error','Error','Failed to load listings. Please try again.');
      }
    };
    loadSites();
  }, []);

  const handleItemPress = (item) => {
    if (!item.id || !item.broker?.id) {
      showToast('error','Error','Invalid land or site scout information.');
      return;
    }
    router.push({
      pathname: '/Properties/LandDetails',
      params: { id: item.id, brokerId: item.broker.id },
    });
  };

  const handleInterestedPress = (item) => {
    if (!item.id || !item.broker?.id) {
      showToast('error','Error','Invalid land or site scout information.');
      return;
    }
    router.push({
      pathname: '/Chat/LandDiscussion',
      params: { landId: item.id, brokerId: item.broker.id },
    });
  };

  return (
    <CategorySection
      title="Lands Near You"
      data={sites.map((site) => ({
        ...site,
        onInterestedPress: () => handleInterestedPress(site),
        accessibilityLabel: `View details for ${site.title} at ${site.location}`,
      }))}
      onItemPress={handleItemPress}
      viewAllRoute="/Properties/LandList"
    />
  );
}
