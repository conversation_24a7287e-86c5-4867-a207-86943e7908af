const ExpressError = require('@build-connect/utils/ExpressError');
const Contractor = require('../../model/Contractor');
const Aadhaar = require('../../model/Aadhaar');
const PAN = require('../../model/Pan');
const Asset = require('../../model/Asset');

exports.getContractorApplicationByUserId = async (req, res) => {
    const userId = req.user.id;

    const contractorProfile = await Contractor.findOne({
        user: userId,
    }).lean();
    if (!contractorProfile) {
        throw new ExpressError('Contractor application not found', 404);
    }

    const aadhaar = await Aadhaar.findOne({ userId }).lean();
    const pan = await PAN.findOne({ userId }).lean();

    const aadhaarAsset = aadhaar
        ? await Asset.findOne({
              entityId: aadhaar._id,
              entityType: 'Aadhaar',
          }).lean()
        : null;
    const panAsset = pan
        ? await Asset.findOne({
              entityId: pan._id,
              entityType: 'PAN',
          }).lean()
        : null;

    res.status(200).json({
        application: {
            specialties: contractorProfile.specialties,
            portfolio: contractorProfile.portfolio,
            experience: contractorProfile.experience,
            serviceAreas: contractorProfile.serviceAreas,
            aadhaarNumber: aadhaar.aadhaarNumber,
            nameOnAadhaar: aadhaar.nameOnAadhaar,
            dateOfBirth: aadhaar.dateOfBirth,
            gender: aadhaar.gender,
            address: aadhaar.address,
            aadhaarAsset: aadhaarAsset ? aadhaarAsset.imageURL : null,
            panNumber: pan.panNumber,
            panName: pan.panName,
            panDateOfBirth: pan.dateOfBirth,
            panAsset: panAsset ? panAsset.imageURL : null,
        },
    });
};
