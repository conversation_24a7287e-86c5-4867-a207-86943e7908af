import React, { useState, useRef, memo, useContext } from "react";
import {
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Alert,
  SafeAreaView,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import Ionicons from "@expo/vector-icons/Ionicons";
import { useRouter } from "expo-router";
import BackButton from "../Components/Shared/BackButton";
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from "../../utils/showToast";

const BrokerApplyIndex = () => {
  const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
  const router = useRouter();
  const [agreed, setAgreed] = useState(false);
  const buttonScale = useRef(new Animated.Value(1)).current;

  // Toggle agreement
  const toggleAgreement = () => setAgreed((prev) => !prev);

  // Handle continue button
  const handleContinue = () => {
    if (!agreed) {
      showToast('error', 'Agreement Required', 'Please agree to the terms and conditions to proceed.');
      return;
    }
    router.push("./Broker/BrokerForm");
  };

  // Button press animation
  const handlePressIn = () => {
    Animated.spring(buttonScale, {
      toValue: 0.95,
      friction: 8,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(buttonScale, {
      toValue: 1,
      friction: 8,
      useNativeDriver: true,
    }).start();
  };

  return (
    <View style={[styles.container]}>
      <LinearGradient
        colors={[theme.PRIMARY, theme.SECONDARY]}
        start={{ x: 0, y: 0.2 }}
        end={{ x: 0.9, y: 0.4 }}
        style={styles.background}
      />
        <BackButton color={theme.WHITE} />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={[styles.termsCard,{shadowColor: theme.PRIMARY,backgroundColor: theme.CARD,}]}>
          <Text style={[styles.termsTitle,{color: theme.PRIMARY,}]}>Terms and Conditions</Text>
          <Text style={[styles.termsText,{color: theme.PRIMARY,}]}>
            By applying to become a Site Scout on Build Connect, you agree to
            the following terms:
          </Text>
          <Text style={[styles.termsItem,{color: theme.TEXT_SECONDARY,}]}>
            • You will provide accurate and truthful information regarding your
            experience and service areas.
          </Text>
          <Text style={[styles.termsItem,{color: theme.TEXT_SECONDARY,}]}>
            • Your application will undergo a verification process, which may
            take up to 7 business days.
          </Text>
          <Text style={[styles.termsItem,{color: theme.TEXT_SECONDARY,}]}>
            • You agree to abide by Build Connect’s code of conduct, including
            fair and transparent dealings with clients.
          </Text>
          <Text style={[styles.termsItem,{color: theme.TEXT_SECONDARY,}]}>
            • Build Connect reserves the right to reject applications or revoke
            Site Scout status for violations of platform policies.
          </Text>
          <Text style={[styles.termsItem,{color: theme.TEXT_SECONDARY,}]}>
            • You will maintain up-to-date profile information and respond
            promptly to client inquiries.
          </Text>
          <Text style={[styles.termsItem,{color: theme.TEXT_SECONDARY,}]}>
            • Build Connect is not liable for disputes between Site Scouts and
            clients but will mediate when necessary.
          </Text>
          <TouchableOpacity
              onPress={toggleAgreement}
              accessibilityLabel="Agree to terms and conditions"
              accessibilityRole="checkbox"
              testID="agreement-checkbox"
            >
          <View style={styles.checkboxContainer}>
              <Ionicons
                name={agreed ? "checkbox" : "square-outline"}
                size={24}
                color={agreed ? theme.PRIMARY : theme.GRAY_LIGHT}
              />
            <Text style={[styles.checkboxLabel,{color: theme.TEXT_PRIMARY,}]}>
              I have read and agree to the terms and conditions
            </Text>
          </View>
          </TouchableOpacity>
        </View>
        <Animated.View style={{ transform: [{ scale: buttonScale }] }}>
          <TouchableOpacity
            style={[styles.continueButton,]}
            onPress={handleContinue}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            accessibilityLabel="Continue to application form"
            accessibilityRole="button"
            testID="continue-button"
          >
            <LinearGradient
              colors={[theme.PRIMARY, theme.SECONDARY]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.buttonGradient}
            >
              <Text style={[styles.buttonText,{color: theme.WHITE,}]}>Continue</Text>
              <Ionicons
                name="arrow-forward"
                size={18}
                color={theme.WHITE}
                style={styles.buttonIcon}
              />
            </LinearGradient>
          </TouchableOpacity>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    ...StyleSheet.absoluteFillObject,
    zIndex: -1,
  },
  scrollContent: {
    paddingVertical: 12,
    paddingHorizontal: 12,
  },
  termsCard: {
    borderRadius: 20,
    padding: 24,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
    marginBottom: 24,
  },
  termsTitle: {
    fontSize: 22,
    fontWeight: "bold",
    marginBottom: 16,
    textAlign: "center",
  },
  termsText: {
    fontSize: 16,
    marginBottom: 12,
  },
  termsItem: {
    fontSize: 15,
    marginBottom: 8,
  },
  checkboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 16,
  },
  checkbox: {
    marginRight: 12,
  },
  checkboxLabel: {
    fontSize: 15,
    flex: 1,
  },
  continueButton: {
    alignSelf: "center",
  },
  disabledButton: {
    opacity: 0.6,
  },
  buttonGradient: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 14,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  buttonIcon: {
    marginLeft: 8,
  },
});

export default memo(BrokerApplyIndex);
