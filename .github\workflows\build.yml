name: build-connect-frontend
on: [pull_request, workflow_dispatch]

concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}
    cancel-in-progress: true

permissions:
    contents: read

jobs:
    build:
        runs-on: ubuntu-latest
        defaults:
            run:
                working-directory: ./frontend
        steps:
            - name: Checkout code
              uses: actions/checkout@v4

            - name: Set up Node.js
              uses: actions/setup-node@v4
              with:
                  node-version: 18

            - name: Cache npm
              uses: actions/cache@v3
              with:
                  path: ~/.npm
                  key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
                  restore-keys: |
                      ${{ runner.os }}-node-

            - name: Install dependencies
              run: npm ci

            - name: Install EAS CLI
              run: npm install -g eas-cli

            - name: Install EAS CLI
              run: npm install -g eas-cli

            - name: EAS Build (Android)
              env:
                  EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
                  EAS_BUILD_ANDROID_KEYSTORE_BASE64: ${{ secrets.EAS_BUILD_ANDROID_KEYSTORE_BASE64 }}
                  EAS_BUILD_ANDROID_KEYSTORE_PASSWORD: ${{ secrets.EAS_BUILD_ANDROID_KEYSTORE_PASSWORD }}
                  EAS_BUILD_ANDROID_KEY_ALIAS: ${{ secrets.EAS_BUILD_ANDROID_KEY_ALIAS }}
                  EAS_BUILD_ANDROID_KEY_PASSWORD: ${{ secrets.EAS_BUILD_ANDROID_KEY_PASSWORD }}
              run: |
                  eas whoami
                  eas build --platform android --non-interactive --profile production