import { privateAPIClient } from "../index";

export const fetchBrokerProfile = async (id) => {
  const url = "/user-service/api/v1/brokers";
  const response = await privateAPIClient.get(url);
  return response.data.broker;
};

export const createBrokerApplication = async (data) => {
  const response = await privateAPIClient.post(
    "/user-service/api/v1/brokers", 
    data,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      transformRequest: (formData, headers) => formData,
    }
  );
  return response.data;
};

export const updateBrokerProfile = async (id, data) => {
  const response = await privateAPIClient.patch(
    `/user-service/api/v1/brokers/${id}`,
    data,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      transformRequest: (formData, headers) => formData,
    }
  );
  return response.data;
};


export const deleteBrokerApplication = async (id) => {
  const response = await privateAPIClient.delete(`/user-service/api/v1/brokers/${id}`);
  return response.data;
};
