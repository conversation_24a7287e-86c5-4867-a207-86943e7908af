import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Pressable,
  TextInput,
  StyleSheet,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  Vibration,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useState, useRef, useEffect,useContext } from "react";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { privateAPIClient, publicAPIClient } from "../../api";
import * as SecureStore from "expo-secure-store";
import BackButton from "../Components/Shared/BackButton";
import { ThemeContext } from '../../context/ThemeContext';
import { showToast } from '../../utils/showToast';

// Get device dimensions for responsive layout
const { width, height } = Dimensions.get("window");

// Mock data for testing
const MOCK_DATA = {
  // Mock successful OTP verification response
  verifyOtpSuccess: {
    success: true,
    accessToken: "mock-access-token-12345",
    sessionID: "mock-session-id-67890"
  },
  // Mock failed OTP verification response
  verifyOtpFailure: {
    success: false,
    message: "Invalid verification code"
  },
  // Mock successful password reset response
  resetPasswordSuccess: {
    success: true
  },
  // Mock failed password reset response
  resetPasswordFailure: {
    success: false,
    message: "Password reset failed"
  }
};

export default function VerifyOTP() {
  const { theme, isDarkMode, toggleTheme } = useContext(ThemeContext);
  // State for OTP input fields
  const [n1, setn1] = useState("");
  const [n2, setn2] = useState("");
  const [n3, setn3] = useState("");
  const [n4, setn4] = useState("");
  const [n5, setn5] = useState("");
  const [n6, setn6] = useState("");
  
  // UI state management
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [otpVerified, setOtpVerified] = useState(false);
  
  // Countdown timer for resend functionality
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [passwordError, setPasswordError] = useState("");

  // Create refs for each input field to manage focus
  const input1Ref = useRef(null);
  const input2Ref = useRef(null);
  const input3Ref = useRef(null);
  const input4Ref = useRef(null);
  const input5Ref = useRef(null);
  const input6Ref = useRef(null);
  const newPasswordRef = useRef(null);

  const router = useRouter();

  // Focus first input on component mount
  useEffect(() => {
    // Small delay to ensure the input is ready
    setTimeout(() => input1Ref.current?.focus(), 100);
  }, []);

  // Countdown timer for resend functionality
  useEffect(() => {
    // Only run countdown if not verified and time remaining
    if (countdown > 0 && !otpVerified) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      // Cleanup timer on unmount or when dependencies change
      return () => clearTimeout(timer);
    } else if (countdown === 0) {
      // Enable resend button when countdown reaches zero
      setCanResend(true);
    }
  }, [countdown, otpVerified]);

  /**
   * Handles input changes and auto-focus to next input
   * @param {string} text - The input text
   * @param {Function} setter - State setter function
   * @param {Object} nextInputRef - Ref to the next input field
   * @param {Object} prevInputRef - Ref to the previous input field
   */
  const handleInputChange = (text, setter, nextInputRef, prevInputRef) => {
    // Only allow single digit
    if (/^[0-9]?$/.test(text)) {
      setter(text);
      // Move to next input if a digit was entered
      if (text.length === 1 && nextInputRef) {
        nextInputRef.current?.focus();
      }
    }
  };

  /**
   * Handles backspace key press for navigation between inputs
   * @param {Object} e - Event object
   * @param {Function} setter - State setter function
   * @param {Object} prevInputRef - Ref to the previous input field
   * @param {string} currentValue - Current input value
   */
  const handleKeyPress = (e, setter, prevInputRef, currentValue) => {
    // If backspace is pressed and current input is empty, focus on previous input
    if (e.nativeEvent.key === "Backspace" && !currentValue && prevInputRef) {
      prevInputRef.current?.focus();
    }
  };

  /**
   * Handles OTP resend request
   * Resets the OTP fields and starts a new countdown
   */
  const handleResendOTP = async () => {
    // Prevent resend if button is disabled
    if (!canResend) return;
    try {
      setIsLoading(true);
      
      // Call the API to resend OTP
      // For testing, you can comment out the API call and use setTimeout instead
      await publicAPIClient.post("/users/resend-otp");
      // Mock API call for testing:
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reset countdown and disable resend button
      setCountdown(30);
      setCanResend(false);
      setError("");
      setn1(""); setn2(""); setn3(""); setn4(""); setn5(""); setn6("");
      setTimeout(() => input1Ref.current?.focus(), 100);
      
      // Provide haptic feedback for better UX
      Vibration.vibrate(100);
      showToast('success', 'OTP Sent', 'A new OTP has been sent to your email.');
    } catch (e) {
      showToast('error', 'Error', e.response?.data?.message || "Failed to resend code");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Verifies the entered OTP
   * If successful, shows the password reset form
   */
  const verifyOTP = async () => {
    // Validate all fields are filled
    if (!n1 || !n2 || !n3 || !n4 || !n5 || !n6) {
      setError("Please enter all 6 digits");
      Vibration.vibrate(200); // Longer vibration for error
      return;
    }

    setIsLoading(true);
    setError("");

    // Combine all digits to form the OTP
    const otp = `${n1}${n2}${n3}${n4}${n5}${n6}`;

    try {
      // Use the correct API endpoint for OTP verification
      // For testing, you can use the mock data instead of making an actual API call
    //   let res = await publicAPIClient.post("/users/verify-otp", { otp });
      
      // Mock API response for testing:
      const res = { data: MOCK_DATA.verifyOtpSuccess };
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (res.data.success) {
        // Store tokens if provided
        if (res.data.accessToken) {
          await SecureStore.setItemAsync("accessToken", res.data.accessToken);
        }
        if (res.data.sessionID) {
          await SecureStore.setItemAsync("sessionID", res.data.sessionID);
        }

        // Set OTP as verified and focus on password field
        setOtpVerified(true);
        setTimeout(() => newPasswordRef.current?.focus(), 300);
        Vibration.vibrate(100); // Short vibration for success
        showToast('success', 'OTP Verified', 'You can now reset your password.');
      } else {
        Vibration.vibrate(200);
        showToast('error', 'Invalid OTP', 'The code you entered is incorrect.');
      }
    } catch (e) {
      Vibration.vibrate(200);
      showToast('error', 'Error', e.response?.data?.message || "Verification failed");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handles password reset after OTP verification
   * Validates password requirements and submits to API
   */
  const resetPassword = async () => {
    // Validate password fields
    if (!newPassword) {
      setPasswordError("Please enter a new password");
      Vibration.vibrate(200);
      return;
    }
    
    if (newPassword.length < 8) {
      setPasswordError("Password must be at least 8 characters");
      Vibration.vibrate(200);
      return;
    }
    
    if (newPassword !== confirmNewPassword) {
      setPasswordError("Passwords do not match");
      Vibration.vibrate(200);
      return;
    }

    setIsLoading(true);
    setPasswordError("");

    // Password strength evaluation
    const evaluatePasswordStrength = (password) => {
        if (!password) return { strength: 'none', score: 0 };
        
        let score = 0;
        
        // Length check
        if (password.length >= 8) score += 1;
        if (password.length >= 12) score += 1;
        
        // Character variety checks
        if (/[A-Z]/.test(password)) score += 1; // Has uppercase
        if (/[a-z]/.test(password)) score += 1; // Has lowercase
        if (/[0-9]/.test(password)) score += 1; // Has number
        if (/[^A-Za-z0-9]/.test(password)) score += 1; // Has special char
        
        // Determine strength category
        let strength = 'weak';
        if (score >= 4) strength = 'medium';
        if (score >= 6) strength = 'strong';
        
        return { strength, score };
    };
    try {
      // Call API to reset password
      // For testing, you can use the mock data instead of making an actual API call
      // const res = await privateAPIClient.post("/users/reset-password", {
      //   password: newPassword,
      // });
      
      // Mock API response for testing:
      const res = { data: MOCK_DATA.resetPasswordSuccess };
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (res.data.success) {
        // Navigate to login screen or dashboard
        router.replace("/auth/Login");
        Vibration.vibrate(100);
        showToast('success', 'Password Reset', 'Your password has been reset successfully.');
      } else {
        setPasswordError("Failed to reset password");
        Vibration.vibrate(200);
        showToast('error', 'Error', 'Failed to reset password.');
      }
    } catch (e) {
      Vibration.vibrate(200);
      showToast('error', 'Error', e.response?.data?.message || "Password reset failed");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <BackButton color={theme.WHITE} />

        {/* Background Image with Gradient Overlay */}
        <View style={styles.backgroundContainer}>
          <Image
            source={require("../../assets/images/background.png")}
            style={styles.backgroundImage}
            resizeMode="cover"
          />
          <LinearGradient
            colors={["rgba(42, 142, 158, 0.7)", theme.PRIMARY]}
            style={styles.backgroundOverlay}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          />
        </View>

        {/* Content Container */}
        <View style={styles.contentContainer}>
          {/* Logo */}
          <View style={styles.logoContainer}>
            <Image
              source={require("../../assets/images/build-connect.jpg")}
              style={[styles.logo, { borderColor: theme.LOGO_BORDER }]}
              resizeMode="contain"
            />
          </View>

          {/* Form Container - Shows either OTP verification or password reset form */}
          <View style={[styles.formContainer,{backgroundColor: theme.CARD,shadowColor: theme.SHADOW,}]}>
            {!otpVerified ? (
              /* OTP Verification Step */
              <>
                <Text style={[styles.title,{color: theme.PRIMARY,}]}>Check your email</Text>
                <Text style={[styles.subtitle,{color:theme.TEXT_SECONDARY}]}>
                  We sent a reset link to your registered email. Enter 6 digit code
                  that mentioned in the email
                </Text>

                {/* Error message */}
                {error ? <Text style={styles.errorText}>{error}</Text> : null}

                {/* OTP Input Fields - 6 separate inputs for better UX */}
                <View style={styles.otpContainer}>
                  {/* Generate OTP inputs dynamically for better maintainability */}
                  {[
                    { value: n1, setter: setn1, ref: input1Ref, prev: null, next: input2Ref },
                    { value: n2, setter: setn2, ref: input2Ref, prev: input1Ref, next: input3Ref },
                    { value: n3, setter: setn3, ref: input3Ref, prev: input2Ref, next: input4Ref },
                    { value: n4, setter: setn4, ref: input4Ref, prev: input3Ref, next: input5Ref },
                    { value: n5, setter: setn5, ref: input5Ref, prev: input4Ref, next: input6Ref },
                    { value: n6, setter: setn6, ref: input6Ref, prev: input5Ref, next: null },
                  ].map((input, index) => (
                    <View 
                      key={index}
                      style={[{borderColor: theme.INPUT_BORDER,backgroundColor: theme.INPUT_BACKGROUND,},
                        styles.otpinputContainer, 
                        input.value ? styles.filledInput : null
                      ]}
                    >
                      <TextInput
                        ref={input.ref}
                        value={input.value}
                        onChangeText={(text) =>
                          handleInputChange(text, input.setter, input.next, input.prev)
                        }
                        onKeyPress={(e) => handleKeyPress(e, input.setter, input.prev, input.value)}
                        placeholderTextColor={theme.TEXT_PLACEHOLDER}
                        style={[styles.input,{color:theme.PRIMARY,}]}
                        inputMode="numeric"
                        maxLength={1}
                        keyboardType="number-pad"
                        returnKeyType={index < 5 ? "next" : "done"}
                        accessible={true}
                        accessibilityLabel={`OTP digit ${index + 1}`}
                      />
                    </View>
                  ))}
                </View>

                {/* Verify Button */}
                <TouchableOpacity
                  onPress={verifyOTP}
                  style={[styles.loginButton, { shadowColor: theme.PRIMARY }]}
                  disabled={isLoading}
                >
                  <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.loginButtonGradient}
                  >
                    {isLoading ? (
                      <View style={styles.loadingContainer}>
                        <ActivityIndicator color="white" size="small" />
                        <Text style={styles.loginButtonText}>Verifying...</Text>
                      </View>
                    ) : (
                      <Text style={styles.loginButtonText}>Verify</Text>
                    )}
                  </LinearGradient>
                </TouchableOpacity>

                {/* Resend Link with Countdown Timer */}
                <View style={styles.signupContainer}>
                  <Text style={[styles.signupText,{color:theme.TEXT_SECONDARY}]}>Haven't received the code?</Text>
                  <Pressable 
                    onPress={handleResendOTP}
                    disabled={!canResend || isLoading}
                    style={({ pressed }) => [
                      { opacity: (pressed || !canResend || isLoading) ? 0.7 : 1 }
                    ]}
                  >
                    <Text style={[
                      { color: theme.PRIMARY},
                      styles.signupLink,
                      !canResend && styles.disabledLink
                    ]}>
                      {canResend ? 'Resend email' : `Resend in ${countdown}s`}
                    </Text>
                  </Pressable>
                </View>
              </>
            ) : (
              /* Password Reset Step - Shown after OTP verification */
              <>
                <Text style={[styles.title,{color: theme.PRIMARY,}]}>Reset Password</Text>
                <Text style={[styles.subtitle,{color:theme.TEXT_SECONDARY}]}>
                  Create a new password for your account
                </Text>

                {/* Password Error message */}
                {passwordError ? <Text style={styles.errorText}>{passwordError}</Text> : null}

                {/* New Password Input */}
                <View style={[styles.passwordinputContainer,{backgroundColor: theme.INPUT_BACKGROUND,borderColor: theme.INPUT_BORDER}]}>
                  <Ionicons
                    name="lock-closed-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                  />
                  <TextInput
                    ref={newPasswordRef}
                    placeholder="New Password"
                    value={newPassword}
                    onChangeText={setNewPassword}
                    placeholderTextColor={theme.TEXT_PLACEHOLDER}
                    style={[styles.passwordInput,{color:theme.TEXT_PRIMARY}]}
                    secureTextEntry={!showPassword}
                  />
                  <TouchableOpacity
                    onPress={() => setShowPassword(!showPassword)}
                    style={styles.passwordToggle}
                  >
                    <Ionicons
                      name={showPassword ? "eye-off-outline" : "eye-outline"}
                      size={22}
                      color={theme.PRIMARY}
                    />
                  </TouchableOpacity>
                </View>

                {/* Password strength indicator */}
                {newPassword ? (
                  <View style={styles.passwordStrengthContainer}>
                    <View style={[
                      styles.passwordStrengthBar,
                      styles[newPassword.length < 6 ? 'weakPassword' : 
                             newPassword.length < 8 ? 'mediumPassword' : 'strongPassword']
                    ]} />
                    <Text style={[styles.passwordStrengthText,{color:theme.TEXT_SECONDARY}]}>
                      {newPassword.length < 6 ? 'Weak' : 
                       newPassword.length < 8 ? 'Medium' : 'Strong'} password
                    </Text>
                  </View>
                ) : null}

                {/* Confirm Password Input */}
                <View style={[styles.passwordinputContainer,{backgroundColor: theme.INPUT_BACKGROUND,borderColor: theme.INPUT_BORDER}]}>
                  <Ionicons
                    name="lock-closed-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                  />
                  <TextInput
                    placeholder="Confirm Password"
                    value={confirmNewPassword}
                    onChangeText={setConfirmNewPassword}
                    placeholderTextColor={theme.TEXT_PLACEHOLDER}
                    style={[styles.passwordInput,{color:theme.TEXT_PRIMARY}]}
                    secureTextEntry={!showConfirmPassword}
                  />
                  <TouchableOpacity
                    onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                    style={styles.passwordToggle}
                  >
                    <Ionicons
                      name={showConfirmPassword ? "eye-off-outline" : "eye-outline"}
                      size={22}
                      color={theme.PRIMARY}
                    />
                  </TouchableOpacity>
                </View>

                {/* Reset Button */}
                <TouchableOpacity
                  onPress={resetPassword}
                  style={[styles.loginButton, { shadowColor: theme.PRIMARY }]}
                  disabled={isLoading}
                >
                  <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    style={styles.loginButtonGradient}
                  >
                    {isLoading ? (
                      <View style={styles.loadingContainer}>
                        <ActivityIndicator color="white" size="small" />
                        <Text style={styles.loginButtonText}>Resetting...</Text>
                      </View>
                    ) : (
                      <Text style={styles.loginButtonText}>Reset Password</Text>
                    )}
                  </LinearGradient>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
  },
  backgroundContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    height: height * 0.6,
    zIndex: -1,
  },
  backgroundImage: {
    width: "100%",
    height: "100%",
  },
  backgroundOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  contentContainer: {
    flex: 1,
    alignItems: "center",
  },
  logoContainer: {
    alignItems: "center",
    marginTop: height * 0.02,
    marginBottom: height * 0.04,
  },
  logo: {
    width: 120,
    height: 120,
    borderWidth: 3,
    borderRadius: 60,
  },
  formContainer: {
    width: "90%",
    maxWidth: 400,
    borderRadius: 20,
    padding: 24,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 24,
    textAlign: "center",
  },
  errorText: {
    color: "red",
    marginBottom: 10,
    textAlign: "center",
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
    width: '100%',
    paddingHorizontal: 4,
  },
  otpinputContainer: {
    width: '15%', // Use percentage instead of fixed width
    maxWidth: 50, // Add maximum width
    minWidth: 40, // Add minimum width
    aspectRatio: 1, // Make it square for better appearance
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 12,
    marginHorizontal: 4, // Use horizontal margin instead of just right margin
    paddingHorizontal: 0, // Remove horizontal padding
    height: undefined, // Remove fixed height
    justifyContent: 'center', // Center content vertically
    alignItems: 'center', // Center content horizontally
  },
  filledInput: {
    backgroundColor: "rgba(87, 115, 153, 0.05)",
  },
  input: {
    width: '100%',
    textAlign: 'center',
    fontSize: 20,
    padding: 0, // Remove padding to prevent layout issues
  },
  passwordinputContainer: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 12,
    marginBottom: 16,
    paddingHorizontal: 12,
    height: 56,
  },
  inputIcon: {
    marginRight: 12,
  },
  passwordInput: {
    flex: 1,
    height: "100%",
    fontSize: 12,
  },
  passwordToggle: {
    padding: 8,
  },
  passwordStrengthContainer: {
    marginBottom: 16,
  },
  passwordStrengthBar: {
    height: 4,
    borderRadius: 2,
    marginBottom: 6,
  },
  weakPassword: {
    backgroundColor: "#FF6347", // Tomato red for weak passwords
  },
  mediumPassword: {
    backgroundColor: "#FFD700", // Gold for medium strength passwords
  },
  strongPassword: {
    backgroundColor: "#32CD32", // Lime green for strong passwords
  },
  passwordStrengthText: {
    fontSize: 12,
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  loginButton: {
    borderRadius: 12,
    overflow: "hidden",
    marginBottom: 14,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  loginButtonGradient: {
    paddingVertical: 16,
    alignItems: "center",
  },
  loginButtonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 8,
  },
  signupContainer: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
  },
  signupText: {
    fontSize: 14,
    marginRight: 4,
  },
  signupLink: {
    fontSize: 14,
    fontWeight: "bold",
  },
  disabledLink: {
    color: "#ccc",
  },
});
