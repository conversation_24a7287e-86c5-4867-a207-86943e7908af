import React, { createContext, useState, useEffect } from 'react';
import { Appearance } from 'react-native';
import { LIGHT, DARK } from '../constants/Colors';

export const ThemeContext = createContext();

export const ThemeProvider = ({ children }) => {
  const systemTheme = Appearance.getColorScheme();
  const [isDarkMode, setIsDarkMode] = useState(systemTheme === 'dark');

  useEffect(() => {
    const listener = Appearance.addChangeListener(({ colorScheme }) =>
      setIsDarkMode(colorScheme === 'dark')
    );
    return () => listener.remove();
  }, []);

  const theme = isDarkMode ? DARK : LIGHT;

  return (
    <ThemeContext.Provider
      value={{
        isDarkMode,
        theme,
        toggleTheme: () => setIsDarkMode(prev => !prev),
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};
