import { privateAPIClient } from "../index";

// Fetch all contractors for home page cards
export const fetchAllContractors = async () => {
  const response = await privateAPIClient.get("/user-service/api/v1/contractors/all");
  return response.data.contractors;
};

// Fetch individual contractor profile
export const fetchContractorProfileById = async (contractorId) => {
  const response = await privateAPIClient.get(`/user-service/api/v1/contractors/profile/${contractorId}`);
  return response.data.contractor;
};

// Portfolio management functions
export const addPortfolioItem = async (contractorId, formData) => {
  const response = await privateAPIClient.post(
    `/user-service/api/v1/contractors/${contractorId}/portfolio`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      transformRequest: (formData, headers) => formData,
    }
  );
  return response.data;
};

export const updatePortfolioItem = async (contractorId, portfolioItemId, formData) => {
  const response = await privateAPIClient.put(
    `/user-service/api/v1/contractors/${contractorId}/portfolio/${portfolioItemId}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      transformRequest: (formData, headers) => formData,
    }
  );
  return response.data;
};

export const deletePortfolioItem = async (contractorId, portfolioItemId) => {
  const response = await privateAPIClient.delete(
    `/user-service/api/v1/contractors/${contractorId}/portfolio/${portfolioItemId}`
  );
  return response.data;
};

export const createContractorApplication = async (data) => {
  const response = await privateAPIClient.post(
    "/user-service/api/v1/contractors", 
    data,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      transformRequest: (formData, headers) => formData,
    }
  );
  return response.data;
};

export const updateContractorProfile = async (id, data) => {
  const response = await privateAPIClient.patch(
    `/user-service/api/v1/contractors/${id}`,
    data,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      transformRequest: (formData, headers) => formData,
    }
  );
  return response.data;
};

export const deleteContractorApplication = async (id) => {
  const response = await privateAPIClient.delete(`/user-service/api/v1/contractors/${id}`);
  return response.data;
};
