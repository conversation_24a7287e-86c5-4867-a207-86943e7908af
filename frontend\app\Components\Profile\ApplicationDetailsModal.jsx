import React, { useContext } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../../context/ThemeContext';

const ApplicationDetailsModal = ({ visible, onClose, data, onPreview }) => {
  const { theme } = useContext(ThemeContext);
  if (!data) return null;

  const fields = [
    { label: 'Name', value: data.nameOnAadhaar},
    { label: 'Aadhaar Number', value: data.aadhaarNumber },
    { label: 'PAN Number', value: data.panNumber },
    { label: 'Gender', value: data.gender },
    { label: 'Address', value: data.address },
    { label: 'Service Areas', value: data.serviceAreas?.join(', ') || 'N/A' },
    { label: 'Experience', value: data.experience },
    {
      label: 'Date of Birth',
      value: data.dateOfBirth ? new Date(data.dateOfBirth).toLocaleDateString() : 'N/A',
    },
  ];

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, { backgroundColor: theme.CARD }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: theme.TEXT_PRIMARY }]}>
              Application Details
            </Text>
            <TouchableOpacity onPress={onClose}>
              <MaterialIcons
                name="close"
                size={24}
                color={theme.TEXT_PRIMARY}
              />
            </TouchableOpacity>
          </View>
          <View style={styles.tableContainer}>
            {fields.map((field, index) => (
              <View
                key={field.label}
                style={[
                  styles.tableRow,
                  index % 2 === 1 ? { backgroundColor: theme.CARD + '22' } : {},
                  index === fields.length - 1 ? { borderBottomWidth: 0 } : {},
                ]}
              >
                <View style={styles.tableLabel}>
                  <Text style={[styles.modalDetail, { color: theme.TEXT_PRIMARY, fontWeight: 'bold' }]}>
                    {field.label}
                  </Text>
                </View>
                <View style={styles.tableValue}>
                  <Text style={[styles.modalDetail, { color: theme.TEXT_SECONDARY }]}>
                    {field.value}
                  </Text>
                </View>
              </View>
            ))}
          </View>
          <View style={styles.modalButtonContainer}>
            <TouchableOpacity
              onPress={() => onPreview(data.aadhaarDocument, 'Aadhaar')}
              style={styles.modalButton}
            >
              <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.modalButtonGradient}
              >
                <View style={{flexDirection:'row',alignItems:'center', justifyContent:'space-between',paddingHorizontal: 4, gap:8}}>
                <Ionicons name="document-text-outline" size={20} color={theme.WHITE} style={styles.icon} />
                <Text style={[styles.modalButtonText, { color: theme.WHITE }]}>Aadhaar</Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => onPreview(data.panDocument, 'PAN')}
              style={styles.modalButton}
            >
              <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.modalButtonGradient}
              >
                <View style={{flexDirection:'row',alignItems:'center', justifyContent:'space-between',paddingHorizontal: 4, gap:8}}>
                <Ionicons name="document-text-outline" size={20} color={theme.WHITE} style={styles.icon} />
                <Text style={[styles.modalButtonText, { color: theme.WHITE }]}>PAN</Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  tableContainer: {
    marginBottom: 16,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    paddingVertical: 8,
  },
  tableLabel: {
    flex: 1,
    paddingRight: 8,
  },
  tableValue: {
    flex: 2,
    flexWrap: 'wrap',
  },
  modalDetail: {
    fontSize: 16,
  },
  modalButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    marginHorizontal:8,
  },
  modalButtonGradient: {
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 14,
    fontWeight: '700',
  },
});

export default ApplicationDetailsModal;